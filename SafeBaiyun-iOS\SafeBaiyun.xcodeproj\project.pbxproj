// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		A1B2C3D4E5F6789012345678 /* SafeBaiyunApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1B2C3D4E5F6789012345679 /* SafeBaiyunApp.swift */; };
		A1B2C3D4E5F678901234567A /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1B2C3D4E5F678901234567B /* ContentView.swift */; };
		A1B2C3D4E5F678901234567C /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = A1B2C3D4E5F678901234567D /* Assets.xcassets */; };
		A1B2C3D4E5F678901234567E /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = A1B2C3D4E5F678901234567F /* Preview Assets.xcassets */; };
		A1B2C3D4E5F67890123456A0 /* MainView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1B2C3D4E5F67890123456A1 /* MainView.swift */; };
		A1B2C3D4E5F67890123456A2 /* EditView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1B2C3D4E5F67890123456A3 /* EditView.swift */; };
		A1B2C3D4E5F67890123456A4 /* HelpView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1B2C3D4E5F67890123456A5 /* HelpView.swift */; };
		A1B2C3D4E5F67890123456A6 /* BluetoothManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1B2C3D4E5F67890123456A7 /* BluetoothManager.swift */; };
		A1B2C3D4E5F67890123456A8 /* DataRepository.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1B2C3D4E5F67890123456A9 /* DataRepository.swift */; };
		A1B2C3D4E5F67890123456AA /* EncryptionService.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1B2C3D4E5F67890123456AB /* EncryptionService.swift */; };
		A1B2C3D4E5F67890123456AC /* DeviceConfig.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1B2C3D4E5F67890123456AD /* DeviceConfig.swift */; };
		A1B2C3D4E5F67890123456AE /* SafeBaiyunWidget.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1B2C3D4E5F67890123456AF /* SafeBaiyunWidget.swift */; };
		A1B2C3D4E5F67890123456B0 /* WidgetKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A1B2C3D4E5F67890123456B1 /* WidgetKit.framework */; };
		A1B2C3D4E5F67890123456B2 /* SwiftUI.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = A1B2C3D4E5F67890123456B3 /* SwiftUI.framework */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		A1B2C3D4E5F67890123456B4 /* SafeBaiyun.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = SafeBaiyun.app; sourceTree = BUILT_PRODUCTS_DIR; };
		A1B2C3D4E5F6789012345679 /* SafeBaiyunApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SafeBaiyunApp.swift; sourceTree = "<group>"; };
		A1B2C3D4E5F678901234567B /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		A1B2C3D4E5F678901234567D /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		A1B2C3D4E5F678901234567F /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		A1B2C3D4E5F67890123456A1 /* MainView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MainView.swift; sourceTree = "<group>"; };
		A1B2C3D4E5F67890123456A3 /* EditView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EditView.swift; sourceTree = "<group>"; };
		A1B2C3D4E5F67890123456A5 /* HelpView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HelpView.swift; sourceTree = "<group>"; };
		A1B2C3D4E5F67890123456A7 /* BluetoothManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BluetoothManager.swift; sourceTree = "<group>"; };
		A1B2C3D4E5F67890123456A9 /* DataRepository.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DataRepository.swift; sourceTree = "<group>"; };
		A1B2C3D4E5F67890123456AB /* EncryptionService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EncryptionService.swift; sourceTree = "<group>"; };
		A1B2C3D4E5F67890123456AD /* DeviceConfig.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DeviceConfig.swift; sourceTree = "<group>"; };
		A1B2C3D4E5F67890123456AF /* SafeBaiyunWidget.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SafeBaiyunWidget.swift; sourceTree = "<group>"; };
		A1B2C3D4E5F67890123456B1 /* WidgetKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = WidgetKit.framework; path = System/Library/Frameworks/WidgetKit.framework; sourceTree = SDKROOT; };
		A1B2C3D4E5F67890123456B3 /* SwiftUI.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SwiftUI.framework; path = System/Library/Frameworks/SwiftUI.framework; sourceTree = SDKROOT; };
		A1B2C3D4E5F67890123456B5 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		A1B2C3D4E5F67890123456B6 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		A1B2C3D4E5F67890123456B7 = {
			isa = PBXGroup;
			children = (
				A1B2C3D4E5F67890123456B8 /* SafeBaiyun */,
				A1B2C3D4E5F67890123456C9 /* SafeBaiyunWidget */,
				A1B2C3D4E5F67890123456CA /* Frameworks */,
				A1B2C3D4E5F67890123456CB /* Products */,
			);
			sourceTree = "<group>";
		};
		A1B2C3D4E5F67890123456CB /* Products */ = {
			isa = PBXGroup;
			children = (
				A1B2C3D4E5F67890123456B4 /* SafeBaiyun.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		A1B2C3D4E5F67890123456B8 /* SafeBaiyun */ = {
			isa = PBXGroup;
			children = (
				A1B2C3D4E5F6789012345679 /* SafeBaiyunApp.swift */,
				A1B2C3D4E5F678901234567B /* ContentView.swift */,
				A1B2C3D4E5F67890123456CC /* Views */,
				A1B2C3D4E5F67890123456CD /* Services */,
				A1B2C3D4E5F67890123456CE /* Models */,
				A1B2C3D4E5F678901234567D /* Assets.xcassets */,
				A1B2C3D4E5F67890123456B5 /* Info.plist */,
				A1B2C3D4E5F67890123456CF /* Preview Content */,
			);
			path = SafeBaiyun;
			sourceTree = "<group>";
		};
		A1B2C3D4E5F67890123456C9 /* SafeBaiyunWidget */ = {
			isa = PBXGroup;
			children = (
				A1B2C3D4E5F67890123456AF /* SafeBaiyunWidget.swift */,
			);
			path = SafeBaiyunWidget;
			sourceTree = "<group>";
		};
		A1B2C3D4E5F67890123456CA /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				A1B2C3D4E5F67890123456B1 /* WidgetKit.framework */,
				A1B2C3D4E5F67890123456B3 /* SwiftUI.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		A1B2C3D4E5F67890123456CC /* Views */ = {
			isa = PBXGroup;
			children = (
				A1B2C3D4E5F67890123456A1 /* MainView.swift */,
				A1B2C3D4E5F67890123456A3 /* EditView.swift */,
				A1B2C3D4E5F67890123456A5 /* HelpView.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		A1B2C3D4E5F67890123456CD /* Services */ = {
			isa = PBXGroup;
			children = (
				A1B2C3D4E5F67890123456A7 /* BluetoothManager.swift */,
				A1B2C3D4E5F67890123456A9 /* DataRepository.swift */,
				A1B2C3D4E5F67890123456AB /* EncryptionService.swift */,
			);
			path = Services;
			sourceTree = "<group>";
		};
		A1B2C3D4E5F67890123456CE /* Models */ = {
			isa = PBXGroup;
			children = (
				A1B2C3D4E5F67890123456AD /* DeviceConfig.swift */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		A1B2C3D4E5F67890123456CF /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				A1B2C3D4E5F678901234567F /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		A1B2C3D4E5F67890123456D0 /* SafeBaiyun */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A1B2C3D4E5F67890123456D1 /* Build configuration list for PBXNativeTarget "SafeBaiyun" */;
			buildPhases = (
				A1B2C3D4E5F67890123456D2 /* Sources */,
				A1B2C3D4E5F67890123456B6 /* Frameworks */,
				A1B2C3D4E5F67890123456D3 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = SafeBaiyun;
			productName = SafeBaiyun;
			productReference = A1B2C3D4E5F67890123456B4 /* SafeBaiyun.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		A1B2C3D4E5F67890123456D4 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1500;
				LastUpgradeCheck = 1500;
				TargetAttributes = {
					A1B2C3D4E5F67890123456D0 = {
						CreatedOnToolsVersion = 15.0;
					};
				};
			};
			buildConfigurationList = A1B2C3D4E5F67890123456D5 /* Build configuration list for PBXProject "SafeBaiyun" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				"zh-Hans",
			);
			mainGroup = A1B2C3D4E5F67890123456B7;
			productRefGroup = A1B2C3D4E5F67890123456CB /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				A1B2C3D4E5F67890123456D0 /* SafeBaiyun */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		A1B2C3D4E5F67890123456D3 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A1B2C3D4E5F678901234567E /* Preview Assets.xcassets in Resources */,
				A1B2C3D4E5F678901234567C /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		A1B2C3D4E5F67890123456D2 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A1B2C3D4E5F678901234567A /* ContentView.swift in Sources */,
				A1B2C3D4E5F67890123456A0 /* MainView.swift in Sources */,
				A1B2C3D4E5F67890123456A2 /* EditView.swift in Sources */,
				A1B2C3D4E5F67890123456A4 /* HelpView.swift in Sources */,
				A1B2C3D4E5F67890123456A6 /* BluetoothManager.swift in Sources */,
				A1B2C3D4E5F67890123456A8 /* DataRepository.swift in Sources */,
				A1B2C3D4E5F67890123456AA /* EncryptionService.swift in Sources */,
				A1B2C3D4E5F67890123456AC /* DeviceConfig.swift in Sources */,
				A1B2C3D4E5F6789012345678 /* SafeBaiyunApp.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		A1B2C3D4E5F67890123456D6 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		A1B2C3D4E5F67890123456D7 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		A1B2C3D4E5F67890123456D8 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"SafeBaiyun/Preview Content\"";
				DEVELOPMENT_TEAM = "";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = SafeBaiyun/Info.plist;
				INFOPLIST_KEY_NSBluetoothAlwaysUsageDescription = "This app needs Bluetooth access to unlock doors via Bluetooth LE connection.";
				INFOPLIST_KEY_NSBluetoothPeripheralUsageDescription = "This app needs Bluetooth access to unlock doors via Bluetooth LE connection.";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.1;
				PRODUCT_BUNDLE_IDENTIFIER = cn.huacheng.safebaiyun;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		A1B2C3D4E5F67890123456D9 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"SafeBaiyun/Preview Content\"";
				DEVELOPMENT_TEAM = "";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = SafeBaiyun/Info.plist;
				INFOPLIST_KEY_NSBluetoothAlwaysUsageDescription = "This app needs Bluetooth access to unlock doors via Bluetooth LE connection.";
				INFOPLIST_KEY_NSBluetoothPeripheralUsageDescription = "This app needs Bluetooth access to unlock doors via Bluetooth LE connection.";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.1;
				PRODUCT_BUNDLE_IDENTIFIER = cn.huacheng.safebaiyun;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		A1B2C3D4E5F67890123456D5 /* Build configuration list for PBXProject "SafeBaiyun" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A1B2C3D4E5F67890123456D6 /* Debug */,
				A1B2C3D4E5F67890123456D7 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A1B2C3D4E5F67890123456D1 /* Build configuration list for PBXNativeTarget "SafeBaiyun" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A1B2C3D4E5F67890123456D8 /* Debug */,
				A1B2C3D4E5F67890123456D9 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = A1B2C3D4E5F67890123456D4 /* Project object */;
}
