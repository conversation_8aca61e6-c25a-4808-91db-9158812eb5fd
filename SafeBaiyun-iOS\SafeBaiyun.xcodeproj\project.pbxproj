// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		1A2B3C4D5E6F7890ABCD /* SafeBaiyunApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A2B3C4D5E6F7890ABCE /* SafeBaiyunApp.swift */; };
		1A2B3C4D5E6F7890ABCF /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A2B3C4D5E6F7890ABD0 /* ContentView.swift */; };
		1A2B3C4D5E6F7890ABD1 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 1A2B3C4D5E6F7890ABD2 /* Assets.xcassets */; };
		1A2B3C4D5E6F7890ABD3 /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 1A2B3C4D5E6F7890ABD4 /* Preview Assets.xcassets */; };
		1A2B3C4D5E6F7890ABD5 /* MainView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A2B3C4D5E6F7890ABD6 /* MainView.swift */; };
		1A2B3C4D5E6F7890ABD7 /* EditView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A2B3C4D5E6F7890ABD8 /* EditView.swift */; };
		1A2B3C4D5E6F7890ABD9 /* HelpView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A2B3C4D5E6F7890ABDA /* HelpView.swift */; };
		1A2B3C4D5E6F7890ABDB /* BluetoothManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A2B3C4D5E6F7890ABDC /* BluetoothManager.swift */; };
		1A2B3C4D5E6F7890ABDD /* DataRepository.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A2B3C4D5E6F7890ABDE /* DataRepository.swift */; };
		1A2B3C4D5E6F7890ABDF /* EncryptionService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A2B3C4D5E6F7890ABE0 /* EncryptionService.swift */; };
		1A2B3C4D5E6F7890ABE1 /* DeviceConfig.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A2B3C4D5E6F7890ABE2 /* DeviceConfig.swift */; };
		1A2B3C4D5E6F7890ABE3 /* SafeBaiyunWidget.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A2B3C4D5E6F7890ABE4 /* SafeBaiyunWidget.swift */; };
		1A2B3C4D5E6F7890ABE5 /* WidgetKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1A2B3C4D5E6F7890ABE6 /* WidgetKit.framework */; };
		1A2B3C4D5E6F7890ABE7 /* SwiftUI.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1A2B3C4D5E6F7890ABE8 /* SwiftUI.framework */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		1A2B3C4D5E6F7890ABCB /* SafeBaiyun.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = SafeBaiyun.app; sourceTree = BUILT_PRODUCTS_DIR; };
		1A2B3C4D5E6F7890ABCE /* SafeBaiyunApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SafeBaiyunApp.swift; sourceTree = "<group>"; };
		1A2B3C4D5E6F7890ABD0 /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		1A2B3C4D5E6F7890ABD2 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		1A2B3C4D5E6F7890ABD4 /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		1A2B3C4D5E6F7890ABD6 /* MainView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MainView.swift; sourceTree = "<group>"; };
		1A2B3C4D5E6F7890ABD8 /* EditView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EditView.swift; sourceTree = "<group>"; };
		1A2B3C4D5E6F7890ABDA /* HelpView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HelpView.swift; sourceTree = "<group>"; };
		1A2B3C4D5E6F7890ABDC /* BluetoothManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BluetoothManager.swift; sourceTree = "<group>"; };
		1A2B3C4D5E6F7890ABDE /* DataRepository.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DataRepository.swift; sourceTree = "<group>"; };
		1A2B3C4D5E6F7890ABE0 /* EncryptionService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EncryptionService.swift; sourceTree = "<group>"; };
		1A2B3C4D5E6F7890ABE2 /* DeviceConfig.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DeviceConfig.swift; sourceTree = "<group>"; };
		1A2B3C4D5E6F7890ABE4 /* SafeBaiyunWidget.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SafeBaiyunWidget.swift; sourceTree = "<group>"; };
		1A2B3C4D5E6F7890ABE6 /* WidgetKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = WidgetKit.framework; path = System/Library/Frameworks/WidgetKit.framework; sourceTree = SDKROOT; };
		1A2B3C4D5E6F7890ABE8 /* SwiftUI.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SwiftUI.framework; path = System/Library/Frameworks/SwiftUI.framework; sourceTree = SDKROOT; };
		1A2B3C4D5E6F7890ABEA /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		1A2B3C4D5E6F7890ABC8 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		1A2B3C4D5E6F7890ABC2 = {
			isa = PBXGroup;
			children = (
				1A2B3C4D5E6F7890ABCD /* SafeBaiyun */,
				1A2B3C4D5E6F7890ABEB /* SafeBaiyunWidget */,
				1A2B3C4D5E6F7890ABEC /* Frameworks */,
				1A2B3C4D5E6F7890ABCC /* Products */,
			);
			sourceTree = "<group>";
		};
		1A2B3C4D5E6F7890ABCC /* Products */ = {
			isa = PBXGroup;
			children = (
				1A2B3C4D5E6F7890ABCB /* SafeBaiyun.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		1A2B3C4D5E6F7890ABCD /* SafeBaiyun */ = {
			isa = PBXGroup;
			children = (
				1A2B3C4D5E6F7890ABCE /* SafeBaiyunApp.swift */,
				1A2B3C4D5E6F7890ABD0 /* ContentView.swift */,
				1A2B3C4D5E6F7890ABEF /* Views */,
				1A2B3C4D5E6F7890ABF0 /* Services */,
				1A2B3C4D5E6F7890ABF1 /* Models */,
				1A2B3C4D5E6F7890ABD2 /* Assets.xcassets */,
				1A2B3C4D5E6F7890ABEA /* Info.plist */,
				1A2B3C4D5E6F7890ABF2 /* Preview Content */,
			);
			path = SafeBaiyun;
			sourceTree = "<group>";
		};
		1A2B3C4D5E6F7890ABEB /* SafeBaiyunWidget */ = {
			isa = PBXGroup;
			children = (
				1A2B3C4D5E6F7890ABE4 /* SafeBaiyunWidget.swift */,
			);
			path = SafeBaiyunWidget;
			sourceTree = "<group>";
		};
		1A2B3C4D5E6F7890ABEC /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				1A2B3C4D5E6F7890ABE6 /* WidgetKit.framework */,
				1A2B3C4D5E6F7890ABE8 /* SwiftUI.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		1A2B3C4D5E6F7890ABEF /* Views */ = {
			isa = PBXGroup;
			children = (
				1A2B3C4D5E6F7890ABD6 /* MainView.swift */,
				1A2B3C4D5E6F7890ABD8 /* EditView.swift */,
				1A2B3C4D5E6F7890ABDA /* HelpView.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		1A2B3C4D5E6F7890ABF0 /* Services */ = {
			isa = PBXGroup;
			children = (
				1A2B3C4D5E6F7890ABDC /* BluetoothManager.swift */,
				1A2B3C4D5E6F7890ABDE /* DataRepository.swift */,
				1A2B3C4D5E6F7890ABE0 /* EncryptionService.swift */,
			);
			path = Services;
			sourceTree = "<group>";
		};
		1A2B3C4D5E6F7890ABF1 /* Models */ = {
			isa = PBXGroup;
			children = (
				1A2B3C4D5E6F7890ABE2 /* DeviceConfig.swift */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		1A2B3C4D5E6F7890ABF2 /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				1A2B3C4D5E6F7890ABD4 /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		1A2B3C4D5E6F7890ABCA /* SafeBaiyun */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1A2B3C4D5E6F7890ABF3 /* Build configuration list for PBXNativeTarget "SafeBaiyun" */;
			buildPhases = (
				1A2B3C4D5E6F7890ABC7 /* Sources */,
				1A2B3C4D5E6F7890ABC8 /* Frameworks */,
				1A2B3C4D5E6F7890ABC9 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = SafeBaiyun;
			productName = SafeBaiyun;
			productReference = 1A2B3C4D5E6F7890ABCB /* SafeBaiyun.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		1A2B3C4D5E6F7890ABC3 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1500;
				LastUpgradeCheck = 1500;
				TargetAttributes = {
					1A2B3C4D5E6F7890ABCA = {
						CreatedOnToolsVersion = 15.0;
					};
				};
			};
			buildConfigurationList = 1A2B3C4D5E6F7890ABC6 /* Build configuration list for PBXProject "SafeBaiyun" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				"zh-Hans",
			);
			mainGroup = 1A2B3C4D5E6F7890ABC2;
			productRefGroup = 1A2B3C4D5E6F7890ABCC /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				1A2B3C4D5E6F7890ABCA /* SafeBaiyun */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		1A2B3C4D5E6F7890ABC9 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1A2B3C4D5E6F7890ABD3 /* Preview Assets.xcassets in Resources */,
				1A2B3C4D5E6F7890ABD1 /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		1A2B3C4D5E6F7890ABC7 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1A2B3C4D5E6F7890ABCF /* ContentView.swift in Sources */,
				1A2B3C4D5E6F7890ABD5 /* MainView.swift in Sources */,
				1A2B3C4D5E6F7890ABD7 /* EditView.swift in Sources */,
				1A2B3C4D5E6F7890ABD9 /* HelpView.swift in Sources */,
				1A2B3C4D5E6F7890ABDB /* BluetoothManager.swift in Sources */,
				1A2B3C4D5E6F7890ABDD /* DataRepository.swift in Sources */,
				1A2B3C4D5E6F7890ABDF /* EncryptionService.swift in Sources */,
				1A2B3C4D5E6F7890ABE1 /* DeviceConfig.swift in Sources */,
				1A2B3C4D5E6F7890ABCD /* SafeBaiyunApp.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		1A2B3C4D5E6F7890ABC4 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		1A2B3C4D5E6F7890ABC5 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		1A2B3C4D5E6F7890ABF4 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"SafeBaiyun/Preview Content\"";
				DEVELOPMENT_TEAM = "";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = SafeBaiyun/Info.plist;
				INFOPLIST_KEY_NSBluetoothAlwaysUsageDescription = "This app needs Bluetooth access to unlock doors via Bluetooth LE connection.";
				INFOPLIST_KEY_NSBluetoothPeripheralUsageDescription = "This app needs Bluetooth access to unlock doors via Bluetooth LE connection.";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.1;
				PRODUCT_BUNDLE_IDENTIFIER = cn.huacheng.safebaiyun;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		1A2B3C4D5E6F7890ABF5 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"SafeBaiyun/Preview Content\"";
				DEVELOPMENT_TEAM = "";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = SafeBaiyun/Info.plist;
				INFOPLIST_KEY_NSBluetoothAlwaysUsageDescription = "This app needs Bluetooth access to unlock doors via Bluetooth LE connection.";
				INFOPLIST_KEY_NSBluetoothPeripheralUsageDescription = "This app needs Bluetooth access to unlock doors via Bluetooth LE connection.";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.1;
				PRODUCT_BUNDLE_IDENTIFIER = cn.huacheng.safebaiyun;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		1A2B3C4D5E6F7890ABC6 /* Build configuration list for PBXProject "SafeBaiyun" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1A2B3C4D5E6F7890ABC4 /* Debug */,
				1A2B3C4D5E6F7890ABC5 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		1A2B3C4D5E6F7890ABF3 /* Build configuration list for PBXNativeTarget "SafeBaiyun" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1A2B3C4D5E6F7890ABF4 /* Debug */,
				1A2B3C4D5E6F7890ABF5 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 1A2B3C4D5E6F7890ABC3 /* Project object */;
}
