#!/usr/bin/env python3
"""
SafeBaiyun iOS项目资源验证脚本
检查Assets.xcassets中的资源是否完整
"""

import os
import json
import sys

def check_file_exists(filepath, description):
    """检查文件是否存在"""
    if os.path.exists(filepath):
        print(f"✅ {description}: {filepath}")
        return True
    else:
        print(f"❌ {description}: {filepath} (缺失)")
        return False

def check_json_file(filepath, description):
    """检查JSON文件是否有效"""
    if not os.path.exists(filepath):
        print(f"❌ {description}: {filepath} (缺失)")
        return False
    
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            json.load(f)
        print(f"✅ {description}: {filepath} (JSON格式正确)")
        return True
    except json.JSONDecodeError as e:
        print(f"❌ {description}: {filepath} (JSON格式错误: {e})")
        return False

def main():
    """主验证函数"""
    print("🔍 SafeBaiyun iOS项目资源验证")
    print("=" * 50)
    
    base_dir = "SafeBaiyun/Assets.xcassets"
    all_good = True
    
    # 检查主Assets目录
    if not os.path.exists(base_dir):
        print(f"❌ Assets目录不存在: {base_dir}")
        return False
    
    print(f"📁 Assets目录: {base_dir}")
    
    # 1. 检查主Contents.json
    print("\n📋 检查主配置文件:")
    main_contents = os.path.join(base_dir, "Contents.json")
    all_good &= check_json_file(main_contents, "主Contents.json")
    
    # 2. 检查AppIcon.appiconset
    print("\n🎨 检查应用图标:")
    appicon_dir = os.path.join(base_dir, "AppIcon.appiconset")
    appicon_contents = os.path.join(appicon_dir, "Contents.json")
    
    if check_json_file(appicon_contents, "AppIcon Contents.json"):
        # 读取AppIcon配置并检查图标文件
        try:
            with open(appicon_contents, 'r', encoding='utf-8') as f:
                appicon_config = json.load(f)
            
            icon_count = 0
            missing_icons = 0
            
            for image in appicon_config.get('images', []):
                filename = image.get('filename')
                if filename:
                    icon_path = os.path.join(appicon_dir, filename)
                    icon_count += 1
                    if not check_file_exists(icon_path, f"图标文件"):
                        missing_icons += 1
            
            print(f"📊 图标统计: 总计{icon_count}个，缺失{missing_icons}个")
            if missing_icons == 0:
                print("✅ 所有图标文件都存在")
            else:
                all_good = False
                
        except Exception as e:
            print(f"❌ 读取AppIcon配置失败: {e}")
            all_good = False
    else:
        all_good = False
    
    # 3. 检查AccentColor.colorset
    print("\n🎨 检查主题色:")
    accent_dir = os.path.join(base_dir, "AccentColor.colorset")
    accent_contents = os.path.join(accent_dir, "Contents.json")
    all_good &= check_json_file(accent_contents, "AccentColor Contents.json")
    
    # 4. 检查其他颜色资源
    print("\n🌈 检查其他颜色资源:")
    colors_dir = os.path.join(base_dir, "Colors")
    if os.path.exists(colors_dir):
        color_sets = [d for d in os.listdir(colors_dir) 
                     if d.endswith('.colorset') and os.path.isdir(os.path.join(colors_dir, d))]
        
        print(f"📊 发现 {len(color_sets)} 个颜色集:")
        for colorset in color_sets:
            colorset_contents = os.path.join(colors_dir, colorset, "Contents.json")
            check_json_file(colorset_contents, f"颜色集 {colorset}")
    else:
        print("ℹ️  没有额外的颜色资源目录")
    
    # 5. 总结
    print("\n" + "=" * 50)
    if all_good:
        print("🎉 资源验证通过！所有必需的资源都已正确配置。")
        print("\n📝 下一步操作:")
        print("1. 在Xcode中打开项目: open SafeBaiyun.xcodeproj")
        print("2. 尝试编译项目: Product > Build (⌘+B)")
        print("3. 如果需要，可以替换占位符图标为实际设计的图标")
        return True
    else:
        print("❌ 资源验证失败！请修复上述问题后重试。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
