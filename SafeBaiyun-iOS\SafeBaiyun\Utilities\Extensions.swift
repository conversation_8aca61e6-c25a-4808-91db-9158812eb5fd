import SwiftUI
import Foundation

// MARK: - String Extensions
extension String {
    /// Validates if string is a valid MAC address
    var isValidMacAddress: Bool {
        let macRegex = "^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$"
        let macPredicate = NSPredicate(format: "SELF MATCHES %@", macRegex)
        return macPredicate.evaluate(with: self)
    }
    
    /// Formats string as MAC address with colons
    var formattedAsMacAddress: String {
        let cleanInput = self.uppercased().replacingOccurrences(of: "[^0-9A-F]", with: "", options: .regularExpression)
        let limitedInput = String(cleanInput.prefix(12))
        
        var formatted = ""
        for (index, character) in limitedInput.enumerated() {
            if index > 0 && index % 2 == 0 {
                formatted += ":"
            }
            formatted += String(character)
        }
        
        return formatted
    }
    
    /// Converts hex string to byte array
    var hexToBytes: [UInt8] {
        let cleanHex = self.replacingOccurrences(of: " ", with: "").replacingOccurrences(of: ":", with: "")
        var bytes: [UInt8] = []
        
        for i in stride(from: 0, to: cleanHex.count, by: 2) {
            let startIndex = cleanHex.index(cleanHex.startIndex, offsetBy: i)
            let endIndex = cleanHex.index(startIndex, offsetBy: 2)
            let byteString = String(cleanHex[startIndex..<endIndex])
            
            if let byte = UInt8(byteString, radix: 16) {
                bytes.append(byte)
            }
        }
        
        return bytes
    }
}

// MARK: - Array Extensions
extension Array where Element == UInt8 {
    /// Converts byte array to hex string
    var bytesToHex: String {
        return self.map { String(format: "%02X", $0) }.joined()
    }
    
    /// Converts byte array to hex string with separators
    func bytesToHex(separator: String = " ") -> String {
        return self.map { String(format: "%02X", $0) }.joined(separator: separator)
    }
}

// MARK: - View Extensions
extension View {
    /// Applies a card-like appearance
    func cardStyle() -> some View {
        self
            .background(Color(.systemBackground))
            .cornerRadius(Constants.UI.cornerRadius)
            .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
    
    /// Applies standard button styling
    func buttonStyle(backgroundColor: Color = .blue, foregroundColor: Color = .white) -> some View {
        self
            .foregroundColor(foregroundColor)
            .frame(height: Constants.UI.buttonHeight)
            .frame(maxWidth: .infinity)
            .background(backgroundColor)
            .cornerRadius(Constants.UI.cornerRadius)
    }
    
    /// Adds haptic feedback on tap
    func withHapticFeedback(_ style: HapticFeedbackStyle = .light) -> some View {
        self.onTapGesture {
            switch style {
            case .light:
                HapticFeedbackManager.shared.lightImpact()
            case .medium:
                HapticFeedbackManager.shared.mediumImpact()
            case .heavy:
                HapticFeedbackManager.shared.heavyImpact()
            case .success:
                HapticFeedbackManager.shared.success()
            case .warning:
                HapticFeedbackManager.shared.warning()
            case .error:
                HapticFeedbackManager.shared.error()
            case .selection:
                HapticFeedbackManager.shared.selection()
            }
        }
    }
    
    /// Conditional view modifier
    @ViewBuilder
    func `if`<Content: View>(_ condition: Bool, transform: (Self) -> Content) -> some View {
        if condition {
            transform(self)
        } else {
            self
        }
    }
}

// MARK: - Color Extensions
extension Color {
    /// App primary color
    static let appPrimary = Color.blue
    
    /// App secondary color
    static let appSecondary = Color.gray
    
    /// Success color
    static let success = Color.green
    
    /// Warning color
    static let warning = Color.orange
    
    /// Error color
    static let error = Color.red
    
    /// Creates color from hex string
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0)
        }

        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue:  Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
}

// MARK: - UserDefaults Extensions
extension UserDefaults {
    /// Convenience methods for app-specific keys
    func setMacAddress(_ macAddress: String) {
        set(macAddress, forKey: Constants.UserDefaultsKeys.macAddress)
    }
    
    func getMacAddress() -> String {
        return string(forKey: Constants.UserDefaultsKeys.macAddress) ?? ""
    }
    
    func setEncryptionKey(_ key: String) {
        set(key, forKey: Constants.UserDefaultsKeys.encryptionKey)
    }
    
    func getEncryptionKey() -> String {
        return string(forKey: Constants.UserDefaultsKeys.encryptionKey) ?? ""
    }
    
    func incrementUnlockCount() {
        let currentCount = integer(forKey: Constants.UserDefaultsKeys.unlockCount)
        set(currentCount + 1, forKey: Constants.UserDefaultsKeys.unlockCount)
    }
    
    func getUnlockCount() -> Int {
        return integer(forKey: Constants.UserDefaultsKeys.unlockCount)
    }
}
