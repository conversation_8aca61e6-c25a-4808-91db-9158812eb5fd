import Foundation
import CoreBluetooth
import SwiftUI

/// Bluetooth manager for handling BLE connections and door unlock operations
class BluetoothManager: NSObject, ObservableObject {
    static let shared = BluetoothManager()
    
    @Published var connectionState: BluetoothConnectionState = .disconnected
    @Published var isBluetoothEnabled = false
    
    private var centralManager: CBCentralManager!
    private var peripheral: CBPeripheral?
    private var writeCharacteristic: CBCharacteristic?
    private var deviceConfig: DeviceConfig?
    private var unlockTimer: Timer?
    
    // Bluetooth service and characteristic UUIDs (these need to be determined from the actual device)
    // These are placeholder UUIDs - need to be updated with actual values from the door system
    private let serviceUUID = CBUUID(string: "6E400001-B5A3-F393-E0A9-E50E24DCCA9E")
    private let writeCharacteristicUUID = CBUUID(string: "6E400002-B5A3-F393-E0A9-E50E24DCCA9E")
    
    override init() {
        super.init()
        centralManager = CBCentralManager(delegate: self, queue: nil)
    }
    
    /// Start the unlock process
    func unlock() {
        guard let config = DataRepository.shared.getConfiguration() as DeviceConfig?,
              config.isValid else {
            connectionState = .error("配置无效，请先设置MAC地址和密钥")
            return
        }
        
        guard isBluetoothEnabled else {
            connectionState = .error("蓝牙未开启")
            return
        }
        
        deviceConfig = config
        connectionState = .connecting
        
        // Start scanning for the specific device
        startScanning()
        
        // Set timeout for connection
        unlockTimer = Timer.scheduledTimer(withTimeInterval: 10.0, repeats: false) { _ in
            self.handleTimeout()
        }
    }
    
    /// Start scanning for BLE devices
    private func startScanning() {
        guard centralManager.state == .poweredOn else {
            connectionState = .error("蓝牙未准备就绪")
            return
        }
        
        centralManager.scanForPeripherals(withServices: nil, options: [CBCentralManagerScanOptionAllowDuplicatesKey: false])
        print("开始扫描蓝牙设备...")
    }
    
    /// Stop scanning
    private func stopScanning() {
        centralManager.stopScan()
        print("停止扫描蓝牙设备")
    }
    
    /// Handle connection timeout
    private func handleTimeout() {
        print("连接超时，断开连接")
        disconnect()
        connectionState = .error("连接超时")
    }
    
    /// Disconnect from peripheral
    private func disconnect() {
        unlockTimer?.invalidate()
        unlockTimer = nil
        
        if let peripheral = peripheral {
            centralManager.cancelPeripheralConnection(peripheral)
        }
        
        stopScanning()
        peripheral = nil
        writeCharacteristic = nil
        connectionState = .disconnected
    }
    
    /// Send unlock command to the device
    private func sendUnlockCommand() {
        guard let peripheral = peripheral,
              let characteristic = writeCharacteristic,
              let config = deviceConfig else {
            connectionState = .error("设备未准备就绪")
            return
        }
        
        connectionState = .unlocking
        
        // Create the unlock command data (this needs to match the Android implementation)
        let inputData: [UInt8] = [0x01] // This might need to be adjusted based on actual protocol
        let headerData = config.macAddressBytes
        
        let encryptedData = EncryptionService.encryptData(
            inputData: inputData,
            headerData: headerData,
            keyString: config.encryptionKey
        )
        
        let data = Data(encryptedData)
        
        print("发送解锁命令: \(EncryptionService.bytesToHex(encryptedData))")
        
        peripheral.writeValue(data, for: characteristic, type: .withResponse)
    }
}

// MARK: - CBCentralManagerDelegate
extension BluetoothManager: CBCentralManagerDelegate {
    func centralManagerDidUpdateState(_ central: CBCentralManager) {
        switch central.state {
        case .poweredOn:
            isBluetoothEnabled = true
            print("蓝牙已开启")
        case .poweredOff:
            isBluetoothEnabled = false
            connectionState = .error("蓝牙已关闭")
            print("蓝牙已关闭")
        case .unauthorized:
            isBluetoothEnabled = false
            connectionState = .error("蓝牙权限被拒绝")
            print("蓝牙权限被拒绝")
        case .unsupported:
            isBluetoothEnabled = false
            connectionState = .error("设备不支持蓝牙")
            print("设备不支持蓝牙")
        default:
            isBluetoothEnabled = false
            connectionState = .error("蓝牙状态未知")
            print("蓝牙状态未知: \(central.state.rawValue)")
        }
    }
    
    func centralManager(_ central: CBCentralManager, didDiscover peripheral: CBPeripheral, advertisementData: [String : Any], rssi RSSI: NSNumber) {
        guard deviceConfig != nil else { return }

        // Check if this is the target device by MAC address
        // Note: iOS doesn't directly expose MAC addresses, so we might need to use device name or other identifiers
        if let deviceName = peripheral.name,
           deviceName.contains("Door") || deviceName.contains("Lock") { // Adjust based on actual device name

            print("发现目标设备: \(deviceName)")

            self.peripheral = peripheral
            peripheral.delegate = self

            stopScanning()
            centralManager.connect(peripheral, options: nil)
        }
    }
    
    func centralManager(_ central: CBCentralManager, didConnect peripheral: CBPeripheral) {
        print("已连接到设备: \(peripheral.name ?? "Unknown")")
        connectionState = .connected
        
        // Discover services
        peripheral.discoverServices([serviceUUID])
    }
    
    func centralManager(_ central: CBCentralManager, didFailToConnect peripheral: CBPeripheral, error: Error?) {
        print("连接失败: \(error?.localizedDescription ?? "Unknown error")")
        connectionState = .error("连接失败")
        disconnect()
    }
    
    func centralManager(_ central: CBCentralManager, didDisconnectPeripheral peripheral: CBPeripheral, error: Error?) {
        print("设备已断开连接")
        if connectionState != .disconnected {
            connectionState = .disconnected
        }
    }
}

// MARK: - CBPeripheralDelegate
extension BluetoothManager: CBPeripheralDelegate {
    func peripheral(_ peripheral: CBPeripheral, didDiscoverServices error: Error?) {
        if let error = error {
            print("服务发现失败: \(error.localizedDescription)")
            connectionState = .error("服务发现失败")
            return
        }
        
        guard let services = peripheral.services else {
            connectionState = .error("未找到服务")
            return
        }
        
        for service in services {
            print("发现服务: \(service.uuid)")
            peripheral.discoverCharacteristics([writeCharacteristicUUID], for: service)
        }
    }
    
    func peripheral(_ peripheral: CBPeripheral, didDiscoverCharacteristicsFor service: CBService, error: Error?) {
        if let error = error {
            print("特征发现失败: \(error.localizedDescription)")
            connectionState = .error("特征发现失败")
            return
        }
        
        guard let characteristics = service.characteristics else {
            connectionState = .error("未找到特征")
            return
        }
        
        for characteristic in characteristics {
            print("发现特征: \(characteristic.uuid)")
            
            if characteristic.uuid == writeCharacteristicUUID {
                writeCharacteristic = characteristic
                
                // Send unlock command
                sendUnlockCommand()
                break
            }
        }
    }
    
    func peripheral(_ peripheral: CBPeripheral, didWriteValueFor characteristic: CBCharacteristic, error: Error?) {
        if let error = error {
            print("写入失败: \(error.localizedDescription)")
            connectionState = .error("解锁失败")
        } else {
            print("解锁命令发送成功")
            connectionState = .disconnected
            
            // Show success message
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                // This will be handled by the UI
            }
        }
        
        // Disconnect after operation
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            self.disconnect()
        }
    }
}
