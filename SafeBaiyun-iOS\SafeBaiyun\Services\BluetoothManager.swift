import Foundation
import CoreBluetooth
import SwiftUI

/// Bluetooth manager for handling BLE connections and door unlock operations
class BluetoothManager: NSObject, ObservableObject {
    static let shared = BluetoothManager()
    
    @Published var connectionState: BluetoothConnectionState = .disconnected
    @Published var isBluetoothEnabled = false
    
    private var centralManager: CBCentralManager!
    private var peripheral: CBPeripheral?
    private var writeCharacteristic: CBCharacteristic?
    private var deviceConfig: DeviceConfig?
    private var unlockTimer: Timer?
    
    // Bluetooth service and characteristic UUIDs (from Android implementation)
    // 实际门禁设备使用的UUID，与Android版本保持一致
    private let serviceUUID = CBUUID(string: "14839ac4-7d7e-415c-9a42-167340cf2339")
    private var readCharacteristic: CBCharacteristic?
    private var writeCharacteristic: CBCharacteristic?
    
    override init() {
        super.init()
        centralManager = CBCentralManager(delegate: self, queue: nil)
    }
    
    /// Start the unlock process
    func unlock() {
        guard let config = DataRepository.shared.getConfiguration() as DeviceConfig?,
              config.isValid else {
            connectionState = .error("配置无效，请先设置MAC地址和密钥")
            return
        }
        
        guard isBluetoothEnabled else {
            connectionState = .error("蓝牙未开启")
            return
        }
        
        deviceConfig = config
        connectionState = .connecting
        
        // Start scanning for the specific device
        startScanning()
        
        // Set timeout for connection
        unlockTimer = Timer.scheduledTimer(withTimeInterval: 10.0, repeats: false) { _ in
            self.handleTimeout()
        }
    }
    
    /// Start scanning for BLE devices
    private func startScanning() {
        guard centralManager.state == .poweredOn else {
            connectionState = .error("蓝牙未准备就绪")
            return
        }
        
        centralManager.scanForPeripherals(withServices: nil, options: [CBCentralManagerScanOptionAllowDuplicatesKey: false])
        print("开始扫描蓝牙设备...")
    }
    
    /// Stop scanning
    private func stopScanning() {
        centralManager.stopScan()
        print("停止扫描蓝牙设备")
    }
    
    /// Handle connection timeout
    private func handleTimeout() {
        print("连接超时，断开连接")
        disconnect()
        connectionState = .error("连接超时")
    }
    
    /// Disconnect from peripheral
    private func disconnect() {
        unlockTimer?.invalidate()
        unlockTimer = nil
        
        if let peripheral = peripheral {
            centralManager.cancelPeripheralConnection(peripheral)
        }
        
        stopScanning()
        peripheral = nil
        writeCharacteristic = nil
        connectionState = .disconnected
    }
    
    /// Send unlock command to the device
    private func sendUnlockCommand() {
        // 如果没有读取到数据，使用默认值
        sendUnlockCommand(with: [0x01])
    }

    /// Send unlock command with data read from characteristic
    private func sendUnlockCommand(with readData: [UInt8]) {
        guard let peripheral = peripheral,
              let characteristic = writeCharacteristic,
              let config = deviceConfig else {
            connectionState = .error("设备未准备就绪")
            return
        }

        connectionState = .unlocking

        print("🔐 开始生成解锁密钥...")
        print("读取到的数据: \(readData.map { String(format: "%02X", $0) }.joined(separator: " "))")
        print("MAC地址: \(config.macAddress)")
        print("加密密钥: \(config.encryptionKey)")

        // 使用读取到的数据作为输入数据（按照Android实现）
        let inputData = readData
        let headerData = config.macAddressBytes

        let encryptedData = EncryptionService.encryptData(
            inputData: inputData,
            headerData: headerData,
            keyString: config.encryptionKey
        )

        let data = Data(encryptedData)

        print("🚀 发送解锁命令 (\(encryptedData.count) 字节):")
        print("加密数据: \(EncryptionService.bytesToHex(encryptedData))")

        peripheral.writeValue(data, for: characteristic, type: .withResponse)
    }
}

// MARK: - CBCentralManagerDelegate
extension BluetoothManager: CBCentralManagerDelegate {
    func centralManagerDidUpdateState(_ central: CBCentralManager) {
        switch central.state {
        case .poweredOn:
            isBluetoothEnabled = true
            print("蓝牙已开启")
        case .poweredOff:
            isBluetoothEnabled = false
            connectionState = .error("蓝牙已关闭")
            print("蓝牙已关闭")
        case .unauthorized:
            isBluetoothEnabled = false
            connectionState = .error("蓝牙权限被拒绝")
            print("蓝牙权限被拒绝")
        case .unsupported:
            isBluetoothEnabled = false
            connectionState = .error("设备不支持蓝牙")
            print("设备不支持蓝牙")
        default:
            isBluetoothEnabled = false
            connectionState = .error("蓝牙状态未知")
            print("蓝牙状态未知: \(central.state.rawValue)")
        }
    }
    
    func centralManager(_ central: CBCentralManager, didDiscover peripheral: CBPeripheral, advertisementData: [String : Any], rssi RSSI: NSNumber) {
        guard let config = deviceConfig else { return }

        print("发现设备: \(peripheral.name ?? "Unknown"), UUID: \(peripheral.identifier), RSSI: \(RSSI)")
        print("广告数据: \(advertisementData)")

        // 由于iOS无法直接获取MAC地址，我们需要通过其他方式识别设备
        // 1. 检查是否广播了目标服务UUID
        if let serviceUUIDs = advertisementData[CBAdvertisementDataServiceUUIDsKey] as? [CBUUID],
           serviceUUIDs.contains(serviceUUID) {

            print("✅ 通过服务UUID发现目标设备")
            connectToDevice(peripheral)
            return
        }

        // 2. 检查设备名称（备用方案）
        if let deviceName = peripheral.name,
           !deviceName.isEmpty {
            // 可以根据实际设备名称模式调整
            let namePatterns = ["Door", "Lock", "白云", "BaiYun", "SafeBaiyun"]
            for pattern in namePatterns {
                if deviceName.localizedCaseInsensitiveContains(pattern) {
                    print("✅ 通过设备名称发现可能的目标设备: \(deviceName)")
                    connectToDevice(peripheral)
                    return
                }
            }
        }

        // 3. 如果没有其他识别方式，尝试连接所有BLE设备（谨慎使用）
        // 这种方式仅在测试时使用，生产环境应该有更精确的识别方式
        print("⚠️ 未能通过标准方式识别设备，跳过: \(peripheral.name ?? "Unknown")")
    }

    private func connectToDevice(_ peripheral: CBPeripheral) {
        print("🔗 开始连接设备: \(peripheral.name ?? "Unknown")")

        self.peripheral = peripheral
        peripheral.delegate = self

        stopScanning()
        centralManager.connect(peripheral, options: nil)
    }
    
    func centralManager(_ central: CBCentralManager, didConnect peripheral: CBPeripheral) {
        print("已连接到设备: \(peripheral.name ?? "Unknown")")
        connectionState = .connected
        
        // Discover services
        peripheral.discoverServices([serviceUUID])
    }
    
    func centralManager(_ central: CBCentralManager, didFailToConnect peripheral: CBPeripheral, error: Error?) {
        print("连接失败: \(error?.localizedDescription ?? "Unknown error")")
        connectionState = .error("连接失败")
        disconnect()
    }
    
    func centralManager(_ central: CBCentralManager, didDisconnectPeripheral peripheral: CBPeripheral, error: Error?) {
        print("设备已断开连接")
        if connectionState != .disconnected {
            connectionState = .disconnected
        }
    }
}

// MARK: - CBPeripheralDelegate
extension BluetoothManager: CBPeripheralDelegate {
    func peripheral(_ peripheral: CBPeripheral, didDiscoverServices error: Error?) {
        if let error = error {
            print("服务发现失败: \(error.localizedDescription)")
            connectionState = .error("服务发现失败")
            return
        }
        
        guard let services = peripheral.services else {
            connectionState = .error("未找到服务")
            return
        }
        
        for service in services {
            print("发现服务: \(service.uuid)")
            if service.uuid == serviceUUID {
                print("✅ 找到目标服务，开始发现特征")
                // 发现所有特征，不指定特定UUID，让系统自动发现
                peripheral.discoverCharacteristics(nil, for: service)
                break
            }
        }
    }
    
    func peripheral(_ peripheral: CBPeripheral, didDiscoverCharacteristicsFor service: CBService, error: Error?) {
        if let error = error {
            print("特征发现失败: \(error.localizedDescription)")
            connectionState = .error("特征发现失败")
            return
        }

        guard let characteristics = service.characteristics else {
            connectionState = .error("未找到特征")
            return
        }

        print("发现 \(characteristics.count) 个特征")

        // 根据Android实现，需要找到读写特征
        for characteristic in characteristics {
            let properties = characteristic.properties
            print("特征 \(characteristic.uuid), 属性: \(properties.rawValue)")

            // 检查读属性 (properties & 2) != 0
            if properties.contains(.read) {
                readCharacteristic = characteristic
                print("✅ 找到读特征: \(characteristic.uuid)")
            }

            // 检查写属性 (properties & 8) != 0
            if properties.contains(.write) {
                writeCharacteristic = characteristic
                print("✅ 找到写特征: \(characteristic.uuid)")
            }
        }

        // 按照Android流程：先读取特征值，然后在读取回调中写入数据
        if let readChar = readCharacteristic {
            print("🔍 开始读取特征值...")
            peripheral.readValue(for: readChar)
        } else if let writeChar = writeCharacteristic {
            print("⚠️ 未找到读特征，直接尝试写入")
            sendUnlockCommand()
        } else {
            connectionState = .error("未找到可用的读写特征")
        }
    }

    func peripheral(_ peripheral: CBPeripheral, didUpdateValueFor characteristic: CBCharacteristic, error: Error?) {
        if let error = error {
            print("❌ 读取特征值失败: \(error.localizedDescription)")
            connectionState = .error("读取特征值失败")
            return
        }

        guard let value = characteristic.value else {
            print("❌ 读取到空值")
            connectionState = .error("读取到空值")
            return
        }

        print("📖 读取到特征值: \(value.count) 字节")
        print("特征值内容: \(value.map { String(format: "%02X", $0) }.joined(separator: " "))")

        // 按照Android流程，读取成功后开始写入加密数据
        sendUnlockCommand(with: Array(value))
    }

    func peripheral(_ peripheral: CBPeripheral, didWriteValueFor characteristic: CBCharacteristic, error: Error?) {
        if let error = error {
            print("写入失败: \(error.localizedDescription)")
            connectionState = .error("解锁失败")
        } else {
            print("解锁命令发送成功")
            connectionState = .disconnected
            
            // Show success message
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                // This will be handled by the UI
            }
        }
        
        // Disconnect after operation
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            self.disconnect()
        }
    }
}
