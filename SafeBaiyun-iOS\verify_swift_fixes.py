#!/usr/bin/env python3
"""
验证SafeBaiyun iOS项目Swift编译错误修复
检查修复后的代码是否符合预期
"""

import os
import re

def check_file_exists(filepath, description):
    """检查文件是否存在"""
    if os.path.exists(filepath):
        print(f"✅ {description}: {filepath}")
        return True
    else:
        print(f"❌ {description}: {filepath} (缺失)")
        return False

def check_bluetooth_manager_fixes():
    """检查BluetoothManager.swift的修复"""
    print("\n🔍 检查 BluetoothManager.swift 修复...")
    
    filepath = "SafeBaiyun/Services/BluetoothManager.swift"
    if not os.path.exists(filepath):
        print(f"❌ 文件不存在: {filepath}")
        return False
    
    with open(filepath, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查修复1: 未使用变量的修复
    if "guard deviceConfig != nil else { return }" in content:
        print("✅ 修复1: 未使用变量警告已修复")
        fix1_ok = True
    elif "guard let config = deviceConfig else { return }" in content:
        print("❌ 修复1: 仍然存在未使用变量问题")
        fix1_ok = False
    else:
        print("⚠️  修复1: 无法确定修复状态")
        fix1_ok = False
    
    return fix1_ok

def check_device_config_fixes():
    """检查DeviceConfig.swift的修复"""
    print("\n🔍 检查 DeviceConfig.swift 修复...")
    
    filepath = "SafeBaiyun/Models/DeviceConfig.swift"
    if not os.path.exists(filepath):
        print(f"❌ 文件不存在: {filepath}")
        return False
    
    with open(filepath, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查修复2: Equatable协议的实现
    if "enum BluetoothConnectionState: Equatable" in content:
        print("✅ 修复2: BluetoothConnectionState已实现Equatable协议")
        fix2_ok = True
    else:
        print("❌ 修复2: BluetoothConnectionState未实现Equatable协议")
        fix2_ok = False
    
    # 检查Equatable实现的具体内容
    if "static func == (lhs: BluetoothConnectionState, rhs: BluetoothConnectionState) -> Bool" in content:
        print("✅ 修复2: Equatable协议实现完整")
        fix2_detail_ok = True
    else:
        print("❌ 修复2: Equatable协议实现不完整")
        fix2_detail_ok = False
    
    return fix2_ok and fix2_detail_ok

def check_encryption_service_fixes():
    """检查EncryptionService.swift的修复"""
    print("\n🔍 检查 EncryptionService.swift 修复...")
    
    filepath = "SafeBaiyun/Services/EncryptionService.swift"
    if not os.path.exists(filepath):
        print(f"❌ 文件不存在: {filepath}")
        return False
    
    with open(filepath, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查修复3: 变量可变性
    if "let encryptedData = [UInt8](repeating: 0, count: 8)" in content:
        print("✅ 修复3: encryptedData变量可变性已修复")
        fix3_ok = True
    elif "var encryptedData = [UInt8](repeating: 0, count: 8)" in content:
        print("❌ 修复3: encryptedData仍然使用var声明")
        fix3_ok = False
    else:
        print("⚠️  修复3: 无法确定encryptedData声明状态")
        fix3_ok = False
    
    # 检查修复4: 安全指针使用
    if "withUnsafeBufferPointer" in content:
        print("✅ 修复4: 已使用安全的指针管理方式")
        fix4_ok = True
    else:
        print("❌ 修复4: 仍然使用不安全的指针创建方式")
        fix4_ok = False
    
    # 检查是否移除了不安全的指针创建
    unsafe_patterns = [
        "UnsafePointer(data)",
        "UnsafePointer(desKey)",
        "UnsafeMutablePointer(mutating: encryptedData)"
    ]
    
    unsafe_found = False
    for pattern in unsafe_patterns:
        if pattern in content:
            print(f"❌ 修复4: 仍然存在不安全指针: {pattern}")
            unsafe_found = True
    
    if not unsafe_found:
        print("✅ 修复4: 已移除所有不安全指针创建")
        fix4_detail_ok = True
    else:
        fix4_detail_ok = False
    
    return fix3_ok and fix4_ok and fix4_detail_ok

def check_code_quality():
    """检查代码质量指标"""
    print("\n📊 代码质量检查...")
    
    files_to_check = [
        "SafeBaiyun/Services/BluetoothManager.swift",
        "SafeBaiyun/Models/DeviceConfig.swift",
        "SafeBaiyun/Services/EncryptionService.swift"
    ]
    
    quality_issues = 0
    
    for filepath in files_to_check:
        if not os.path.exists(filepath):
            continue
            
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查常见的代码质量问题
        lines = content.split('\n')
        for i, line in enumerate(lines, 1):
            # 检查过长的行
            if len(line) > 120:
                print(f"⚠️  {filepath}:{i} - 行过长 ({len(line)}字符)")
                quality_issues += 1
    
    if quality_issues == 0:
        print("✅ 代码质量检查通过")
        return True
    else:
        print(f"⚠️  发现 {quality_issues} 个代码质量问题")
        return False

def main():
    """主验证函数"""
    print("🔍 SafeBaiyun iOS项目Swift编译错误修复验证")
    print("=" * 60)
    
    # 检查各个修复
    fix1_ok = check_bluetooth_manager_fixes()
    fix2_ok = check_device_config_fixes()
    fix3_ok = check_encryption_service_fixes()
    quality_ok = check_code_quality()
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 修复验证总结:")
    
    fixes = [
        ("BluetoothManager未使用变量", fix1_ok),
        ("BluetoothConnectionState Equatable协议", fix2_ok),
        ("EncryptionService内存安全", fix3_ok),
        ("代码质量", quality_ok)
    ]
    
    all_fixed = True
    for fix_name, is_ok in fixes:
        status = "✅ 已修复" if is_ok else "❌ 未修复"
        print(f"  {fix_name}: {status}")
        if not is_ok:
            all_fixed = False
    
    print("\n" + "=" * 60)
    if all_fixed:
        print("🎉 所有Swift编译错误修复验证通过！")
        print("\n📝 下一步操作:")
        print("1. 在Mac上的Xcode中打开项目")
        print("2. 编译项目验证修复效果")
        print("3. 运行项目测试功能")
        return True
    else:
        print("❌ 部分修复验证失败，请检查上述问题")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
