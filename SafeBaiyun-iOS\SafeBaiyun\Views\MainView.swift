import SwiftUI

struct MainView: View {
    @StateObject private var dataRepository = DataRepository.shared
    @StateObject private var bluetoothManager = BluetoothManager.shared
    @State private var showEditSheet = false
    @State private var showHelpSheet = false
    @State private var showAlert = false
    @State private var alertMessage = ""
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // Header
                HStack {
                    Text("白云通")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                    
                    Spacer()
                    
                    // Edit button
                    Button(action: {
                        showEditSheet = true
                    }) {
                        Image(systemName: "pencil")
                            .font(.title2)
                            .foregroundColor(.blue)
                    }
                    
                    // Help button
                    Button(action: {
                        showHelpSheet = true
                    }) {
                        Image(systemName: "questionmark.circle")
                            .font(.title2)
                            .foregroundColor(.blue)
                    }
                }
                .padding(.horizontal)
                .padding(.top)
                
                Spacer()
                
                // Main content
                VStack(spacing: 30) {
                    // Connection status
                    VStack(spacing: 10) {
                        Image(systemName: bluetoothStatusIcon)
                            .font(.system(size: 50))
                            .foregroundColor(bluetoothStatusColor)
                        
                        Text(bluetoothManager.connectionState.description)
                            .font(.headline)
                            .foregroundColor(.secondary)
                    }
                    
                    // Unlock button
                    Button(action: {
                        performUnlock()
                    }) {
                        HStack {
                            Image(systemName: "lock.open")
                                .font(.title2)
                            Text("门禁解锁")
                                .font(.title2)
                                .fontWeight(.semibold)
                        }
                        .foregroundColor(.white)
                        .frame(width: 200, height: 60)
                        .background(
                            RoundedRectangle(cornerRadius: 30)
                                .fill(unlockButtonColor)
                        )
                    }
                    .disabled(!canUnlock)
                    .scaleEffect(bluetoothManager.connectionState == .unlocking ? 0.95 : 1.0)
                    .animation(.easeInOut(duration: 0.1), value: bluetoothManager.connectionState)
                    
                    // Configuration status
                    if !dataRepository.hasValidConfiguration() {
                        VStack(spacing: 8) {
                            Image(systemName: "exclamationmark.triangle")
                                .font(.title2)
                                .foregroundColor(.orange)
                            
                            Text("请先配置MAC地址和密钥")
                                .font(.subheadline)
                                .foregroundColor(.orange)
                                .multilineTextAlignment(.center)
                            
                            Button("立即配置") {
                                showEditSheet = true
                            }
                            .font(.caption)
                            .foregroundColor(.blue)
                        }
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.orange.opacity(0.1))
                        )
                    }
                }
                
                Spacer()
                
                // Footer info
                VStack(spacing: 4) {
                    Text("广州市白云区蓝牙门禁")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    if dataRepository.hasValidConfiguration() {
                        Text("MAC: \(dataRepository.deviceConfig.macAddress)")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                            .lineLimit(1)
                    }
                }
                .padding(.bottom)
            }
            .navigationBarHidden(true)
        }
        .sheet(isPresented: $showEditSheet) {
            EditView()
        }
        .sheet(isPresented: $showHelpSheet) {
            HelpView()
        }
        .alert("提示", isPresented: $showAlert) {
            Button("确定", role: .cancel) { }
        } message: {
            Text(alertMessage)
        }
        .onChange(of: bluetoothManager.connectionState) { state in
            handleConnectionStateChange(state)
        }
    }
    
    // MARK: - Computed Properties
    
    private var bluetoothStatusIcon: String {
        switch bluetoothManager.connectionState {
        case .disconnected:
            return "bluetooth.slash"
        case .connecting:
            return "bluetooth"
        case .connected:
            return "bluetooth"
        case .unlocking:
            return "lock.open"
        case .error:
            return "exclamationmark.triangle"
        }
    }
    
    private var bluetoothStatusColor: Color {
        switch bluetoothManager.connectionState {
        case .disconnected:
            return .gray
        case .connecting:
            return .blue
        case .connected:
            return .green
        case .unlocking:
            return .orange
        case .error:
            return .red
        }
    }
    
    private var unlockButtonColor: Color {
        if canUnlock {
            switch bluetoothManager.connectionState {
            case .unlocking:
                return .orange
            default:
                return .blue
            }
        } else {
            return .gray
        }
    }
    
    private var canUnlock: Bool {
        return bluetoothManager.isBluetoothEnabled &&
               dataRepository.hasValidConfiguration() &&
               bluetoothManager.connectionState != .connecting &&
               bluetoothManager.connectionState != .unlocking
    }
    
    // MARK: - Methods
    
    private func performUnlock() {
        guard canUnlock else {
            if !bluetoothManager.isBluetoothEnabled {
                showAlertMessage("请先开启蓝牙")
            } else if !dataRepository.hasValidConfiguration() {
                showAlertMessage("请先配置MAC地址和密钥")
            }
            return
        }
        
        // Haptic feedback
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
        
        bluetoothManager.unlock()
    }
    
    private func handleConnectionStateChange(_ state: BluetoothConnectionState) {
        switch state {
        case .error(let message):
            showAlertMessage(message)
        case .disconnected:
            // Check if this was after a successful unlock
            if case .unlocking = bluetoothManager.connectionState {
                showAlertMessage("门禁解锁成功！")
                
                // Success haptic feedback
                let notificationFeedback = UINotificationFeedbackGenerator()
                notificationFeedback.notificationOccurred(.success)
            }
        default:
            break
        }
    }
    
    private func showAlertMessage(_ message: String) {
        alertMessage = message
        showAlert = true
    }
}

#Preview {
    MainView()
}
