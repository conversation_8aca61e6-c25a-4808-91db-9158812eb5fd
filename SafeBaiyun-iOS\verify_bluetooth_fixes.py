#!/usr/bin/env python3
"""
验证SafeBaiyun iOS蓝牙门禁解锁修复
检查所有关键修复是否正确实施
"""

import os
import re

def check_file_content(filepath, checks, description):
    """检查文件内容是否包含指定的修复"""
    print(f"\n🔍 检查 {description}: {filepath}")
    
    if not os.path.exists(filepath):
        print(f"❌ 文件不存在: {filepath}")
        return False
    
    with open(filepath, 'r', encoding='utf-8') as f:
        content = f.read()
    
    all_passed = True
    for check_name, pattern, required in checks:
        if isinstance(pattern, str):
            found = pattern in content
        else:  # regex pattern
            found = bool(re.search(pattern, content))
        
        if found:
            print(f"✅ {check_name}")
        else:
            status = "❌" if required else "⚠️"
            print(f"{status} {check_name}")
            if required:
                all_passed = False
    
    return all_passed

def main():
    """主验证函数"""
    print("🔍 SafeBaiyun iOS蓝牙门禁解锁修复验证")
    print("=" * 60)
    
    all_checks_passed = True
    
    # 1. 检查BluetoothManager.swift的修复
    bluetooth_manager_checks = [
        ("正确的服务UUID", "14839ac4-7d7e-415c-9a42-167340cf2339", True),
        ("动态特征发现", "discoverCharacteristics(nil, for: service)", True),
        ("读特征处理", "readCharacteristic: CBCharacteristic?", True),
        ("写特征处理", "writeCharacteristic: CBCharacteristic?", True),
        ("读取特征值回调", "didUpdateValueFor characteristic", True),
        ("设备识别优化", "serviceUUIDs.contains(serviceUUID)", True),
        ("详细调试日志", "print(\"发现设备:", True),
        ("连接设备方法", "private func connectToDevice", True),
    ]
    
    result1 = check_file_content(
        "SafeBaiyun/Services/BluetoothManager.swift",
        bluetooth_manager_checks,
        "BluetoothManager.swift修复"
    )
    all_checks_passed &= result1
    
    # 2. 检查Constants.swift的更新
    constants_checks = [
        ("正确的服务UUID常量", "14839ac4-7d7e-415c-9a42-167340cf2339", True),
        ("设备名称模式", "deviceNamePatterns", True),
        ("移除旧的占位符UUID", "6E400001-B5A3-F393-E0A9-E50E24DCCA9E", False),
    ]
    
    result2 = check_file_content(
        "SafeBaiyun/Utilities/Constants.swift",
        constants_checks,
        "Constants.swift更新"
    )
    all_checks_passed &= result2
    
    # 3. 检查调试工具是否创建
    debugger_checks = [
        ("BluetoothDebugger类", "class BluetoothDebugger", True),
        ("深度扫描功能", "func startDeepScan", True),
        ("设备分析功能", "func analyzeAdvertisementData", True),
        ("配置验证功能", "func validateConfiguration", True),
        ("日志导出功能", "func exportLogs", True),
    ]
    
    result3 = check_file_content(
        "SafeBaiyun/Services/BluetoothDebugger.swift",
        debugger_checks,
        "BluetoothDebugger.swift创建"
    )
    all_checks_passed &= result3
    
    # 4. 检查调试界面是否创建
    debug_view_checks = [
        ("BluetoothDebugView结构", "struct BluetoothDebugView", True),
        ("设备列表显示", "List(debugger.discoveredDevices", True),
        ("调试日志显示", "debugLogs", True),
        ("设备详情视图", "DeviceDetailView", True),
        ("兼容性检查", "兼容性检查", True),
    ]
    
    result4 = check_file_content(
        "SafeBaiyun/Views/BluetoothDebugView.swift",
        debug_view_checks,
        "BluetoothDebugView.swift创建"
    )
    all_checks_passed &= result4
    
    # 5. 检查加密服务是否保持兼容
    encryption_checks = [
        ("DES加密函数", "func encryptDES", True),
        ("主加密函数", "static func encryptData(inputData: [UInt8], headerData: [UInt8], keyString: String)", True),
        ("安全指针管理", "withUnsafeBufferPointer", True),
        ("数据包构建", "finalData[0] = 0xA5", True),
        ("校验和计算", "checksum", True),
    ]
    
    result5 = check_file_content(
        "SafeBaiyun/Services/EncryptionService.swift",
        encryption_checks,
        "EncryptionService.swift兼容性"
    )
    all_checks_passed &= result5
    
    # 6. 检查文档是否创建
    doc_files = [
        "BLUETOOTH_TROUBLESHOOTING_GUIDE.md",
        "SWIFT_COMPILATION_FIX_SUMMARY.md"
    ]
    
    for doc_file in doc_files:
        if os.path.exists(doc_file):
            print(f"✅ 文档已创建: {doc_file}")
        else:
            print(f"❌ 文档缺失: {doc_file}")
            all_checks_passed = False
    
    # 7. 检查项目结构完整性
    print(f"\n🔍 检查项目结构完整性")
    
    required_files = [
        "SafeBaiyun/Services/BluetoothManager.swift",
        "SafeBaiyun/Services/EncryptionService.swift",
        "SafeBaiyun/Services/BluetoothDebugger.swift",
        "SafeBaiyun/Views/BluetoothDebugView.swift",
        "SafeBaiyun/Utilities/Constants.swift",
        "SafeBaiyun/Models/DeviceConfig.swift",
    ]
    
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path}")
            missing_files.append(file_path)
            all_checks_passed = False
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 修复验证总结:")
    
    fixes = [
        ("BluetoothManager核心修复", result1),
        ("Constants配置更新", result2),
        ("调试工具创建", result3),
        ("调试界面创建", result4),
        ("加密服务兼容性", result5),
        ("项目结构完整性", len(missing_files) == 0)
    ]
    
    for fix_name, is_ok in fixes:
        status = "✅ 通过" if is_ok else "❌ 失败"
        print(f"  {fix_name}: {status}")
    
    print("\n" + "=" * 60)
    if all_checks_passed:
        print("🎉 所有蓝牙门禁解锁修复验证通过！")
        print("\n📝 下一步操作:")
        print("1. 在真机上编译并安装应用")
        print("2. 使用蓝牙调试工具扫描门禁设备")
        print("3. 验证设备识别和连接流程")
        print("4. 测试实际解锁功能")
        print("5. 根据调试日志进一步优化")
        
        print("\n🔧 调试建议:")
        print("• 在应用中添加调试入口访问BluetoothDebugView")
        print("• 使用深度扫描功能发现所有附近的BLE设备")
        print("• 检查目标设备是否广播正确的服务UUID")
        print("• 对比iOS和Android版本的加密数据输出")
        
        return True
    else:
        print("❌ 部分修复验证失败，请检查上述问题")
        
        if missing_files:
            print(f"\n缺失文件: {', '.join(missing_files)}")
        
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
