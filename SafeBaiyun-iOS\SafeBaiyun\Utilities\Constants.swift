import Foundation

/// App-wide constants
struct Constants {
    
    // MARK: - App Information
    struct App {
        static let name = "白云通"
        static let version = "1.1"
        static let bundleIdentifier = "cn.huacheng.safebaiyun"
    }
    
    // MARK: - URL Schemes
    struct URLSchemes {
        static let main = "safebaiyun"
        static let unlock = "safebaiyun://unlock"
        static let config = "safebaiyun://config"
    }
    
    // MARK: - UserDefaults Keys
    struct UserDefaultsKeys {
        static let macAddress = "mac_address"
        static let encryptionKey = "encryption_key"
        static let hasShownOnboarding = "has_shown_onboarding"
        static let unlockCount = "unlock_count"
    }
    
    // MARK: - Bluetooth Configuration
    struct Bluetooth {
        static let connectionTimeout: TimeInterval = 10.0
        static let scanTimeout: TimeInterval = 5.0

        // 实际门禁设备的服务UUID（与Android版本一致）
        static let serviceUUID = "14839ac4-7d7e-415c-9a42-167340cf2339"

        // 设备识别相关
        static let deviceNamePatterns = ["Door", "Lock", "白云", "<PERSON>Y<PERSON>", "SafeB<PERSON>yun"]
    }
    
    // MARK: - UI Constants
    struct UI {
        static let cornerRadius: CGFloat = 12.0
        static let buttonHeight: CGFloat = 50.0
        static let standardPadding: CGFloat = 16.0
        static let smallPadding: CGFloat = 8.0
    }
    
    // MARK: - Animation Durations
    struct Animation {
        static let short: TimeInterval = 0.2
        static let medium: TimeInterval = 0.3
        static let long: TimeInterval = 0.5
    }
    
    // MARK: - Siri Shortcuts
    struct SiriShortcuts {
        static let unlockActivityType = "cn.huacheng.safebaiyun.unlock"
        static let unlockPersistentIdentifier = "unlock_door"
        static let suggestedInvocationPhrase = "开门"
    }
    
    // MARK: - Widget Configuration
    struct Widget {
        static let kind = "SafeBaiyunWidget"
        static let displayName = "白云通"
        static let description = "快速访问门禁解锁功能"
    }
    
    // MARK: - Error Messages
    struct ErrorMessages {
        static let bluetoothDisabled = "蓝牙未开启，请在设置中开启蓝牙"
        static let bluetoothUnauthorized = "蓝牙权限被拒绝，请在设置中允许蓝牙权限"
        static let bluetoothUnsupported = "设备不支持蓝牙功能"
        static let invalidConfiguration = "配置无效，请检查MAC地址和密钥格式"
        static let connectionTimeout = "连接超时，请确保设备在门禁附近"
        static let connectionFailed = "连接失败，请重试"
        static let unlockFailed = "解锁失败，请检查配置并重试"
        static let deviceNotFound = "未找到门禁设备，请确保设备已开启"
    }
    
    // MARK: - Success Messages
    struct SuccessMessages {
        static let unlockSuccess = "门禁解锁成功！"
        static let configurationSaved = "配置保存成功！"
        static let siriShortcutAdded = "Siri快捷指令添加成功！"
    }
}
