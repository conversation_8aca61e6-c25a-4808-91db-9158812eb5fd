#!/usr/bin/env python3
"""
创建SafeBaiyun应用的占位符图标
生成不同尺寸的PNG图标文件用于iOS应用
"""

import os
from PIL import Image, ImageDraw, ImageFont
import sys

def create_icon(size, filename, text="SB"):
    """创建指定尺寸的图标"""
    # 创建图像
    img = Image.new('RGB', (size, size), color='#3498db')  # 蓝色背景
    draw = ImageDraw.Draw(img)
    
    # 计算字体大小
    font_size = size // 3
    try:
        # 尝试使用系统字体
        font = ImageFont.truetype("arial.ttf", font_size)
    except:
        try:
            font = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", font_size)
        except:
            # 使用默认字体
            font = ImageFont.load_default()
    
    # 计算文字位置（居中）
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    x = (size - text_width) // 2
    y = (size - text_height) // 2
    
    # 绘制文字
    draw.text((x, y), text, fill='white', font=font)
    
    # 保存图像
    img.save(filename, 'PNG')
    print(f"✅ 创建图标: {filename} ({size}x{size})")

def main():
    """主函数"""
    print("🎨 创建SafeBaiyun应用占位符图标...")
    
    # 图标目录
    icon_dir = "SafeBaiyun/Assets.xcassets/AppIcon.appiconset"
    
    # 确保目录存在
    os.makedirs(icon_dir, exist_ok=True)
    
    # 需要的图标尺寸
    icon_sizes = [
        (40, "<EMAIL>"),      # 20pt @2x
        (60, "<EMAIL>"),      # 20pt @3x
        (58, "<EMAIL>"),      # 29pt @2x
        (87, "<EMAIL>"),      # 29pt @3x
        (80, "<EMAIL>"),      # 40pt @2x
        (120, "<EMAIL>"),     # 40pt @3x
        (120, "<EMAIL>"),     # 60pt @2x
        (180, "<EMAIL>"),     # 60pt @3x
        (20, "<EMAIL>"),      # iPad 20pt @1x
        (40, "icon_20pt@2x_ipad.png"), # iPad 20pt @2x
        (29, "<EMAIL>"),      # iPad 29pt @1x
        (58, "icon_29pt@2x_ipad.png"), # iPad 29pt @2x
        (40, "<EMAIL>"),      # iPad 40pt @1x
        (80, "icon_40pt@2x_ipad.png"), # iPad 40pt @2x
        (152, "<EMAIL>"),     # iPad 76pt @2x
        (167, "<EMAIL>"),   # iPad Pro 83.5pt @2x
        (1024, "<EMAIL>"),  # App Store
    ]
    
    # 创建所有尺寸的图标
    for size, filename in icon_sizes:
        filepath = os.path.join(icon_dir, filename)
        create_icon(size, filepath)
    
    print(f"\n🎉 成功创建 {len(icon_sizes)} 个图标文件！")
    print("📝 注意：这些是占位符图标，建议后续替换为实际的应用图标。")

if __name__ == "__main__":
    main()
