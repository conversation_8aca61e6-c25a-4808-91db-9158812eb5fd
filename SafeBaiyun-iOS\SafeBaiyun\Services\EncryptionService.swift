import Foundation
import CommonCrypto

/// Encryption service that replicates the Android LockBiz encryption algorithm
class EncryptionService {
    
    /// Convert hex string to byte array
    static func hexToBytes(_ hexString: String) -> [UInt8] {
        let cleanHex = hexString.replacingOccurrences(of: " ", with: "")
        var bytes: [UInt8] = []
        
        for i in stride(from: 0, to: cleanHex.count, by: 2) {
            let startIndex = cleanHex.index(cleanHex.startIndex, offsetBy: i)
            let endIndex = cleanHex.index(startIndex, offsetBy: 2)
            let byteString = String(cleanHex[startIndex..<endIndex])
            
            if let byte = UInt8(byteString, radix: 16) {
                bytes.append(byte)
            }
        }
        
        return bytes
    }
    
    /// Convert byte to hex string
    static func byteToHex(_ byte: UInt8) -> String {
        return String(format: "%02X", byte)
    }
    
    /// Convert byte array to hex string
    static func bytesToHex(_ bytes: [UInt8]) -> String {
        return bytes.map { byteToHex($0) }.joined()
    }
    
    /// DES encryption using ECB mode with no padding
    static func encryptDES(data: [UInt8], key: [UInt8]) -> [UInt8] {
        guard data.count == 8, key.count >= 8 else {
            print("DES encryption error: Invalid data or key length")
            return []
        }

        // Prepare 8-byte key for DES
        var desKey = [UInt8](repeating: 0, count: 8)
        for i in 0..<min(key.count, 8) {
            desKey[i] = key[i]
        }

        // 使用安全的内存管理方式
        let encryptedData = [UInt8](repeating: 0, count: 8)
        var result = encryptedData

        let status = data.withUnsafeBufferPointer { dataBuffer in
            desKey.withUnsafeBufferPointer { keyBuffer in
                result.withUnsafeMutableBufferPointer { encryptedBuffer in
                    CCCrypt(
                        CCOperation(kCCEncrypt),
                        CCAlgorithm(kCCAlgorithmDES),
                        CCOptions(kCCOptionECBMode),
                        keyBuffer.baseAddress, 8,
                        nil,
                        dataBuffer.baseAddress, 8,
                        encryptedBuffer.baseAddress, 8,
                        nil
                    )
                }
            }
        }

        if status == kCCSuccess {
            return result
        } else {
            print("DES encryption failed with status: \(status)")
            return []
        }
    }
    
    /// Main encryption function that replicates Android LockBiz.encryptData
    static func encryptData(inputData: [UInt8], headerData: [UInt8], keyString: String) -> [UInt8] {
        let keyBytes = hexToBytes(keyString)
        var headerBytesSubset = [UInt8](repeating: 0, count: 4)
        
        // Extract 4 bytes from headerData starting at index 2
        for i in 0..<4 {
            if i + 2 < headerData.count {
                headerBytesSubset[i] = headerData[i + 2]
            }
        }
        
        print("Header data: \(bytesToHex(headerData))")
        print("Header subset: \(bytesToHex(headerBytesSubset))")
        
        // Calculate sum
        var sum = 0
        for byte in inputData {
            sum += Int(byte)
        }
        for byte in keyBytes {
            sum += Int(byte)
        }
        
        // Create sum bytes (little endian)
        let sumBytes: [UInt8] = [UInt8(sum & 0xFF), UInt8((sum >> 8) & 0xFF)]
        
        // Calculate padded length (must be multiple of 8)
        var paddedLength = sumBytes.count + inputData.count
        if paddedLength % 8 != 0 {
            paddedLength = (paddedLength / 8 + 1) * 8
        }
        
        // Create padded data
        var paddedData = [UInt8](repeating: 0, count: paddedLength)
        
        // Copy sum bytes
        for i in 0..<sumBytes.count {
            paddedData[i] = sumBytes[i]
        }
        
        // Copy input data
        for i in 0..<inputData.count {
            paddedData[sumBytes.count + i] = inputData[i]
        }
        
        // Remaining bytes are already 0 (padding)
        
        // Encrypt the padded data
        let encryptedBlock = encryptDES(data: Array(paddedData.prefix(8)), key: keyBytes)
        
        print("Sum: \(sum)")
        print("Before encryption: \(bytesToHex(paddedData))")
        print("After encryption: \(bytesToHex(encryptedBlock))")
        
        // Create final data packet
        let finalDataLength = UInt8(encryptedBlock.count + 12)
        var finalData = [UInt8](repeating: 0, count: Int(finalDataLength))
        
        // Build the packet according to the protocol
        finalData[0] = 0xA5  // -91 in signed byte
        finalData[1] = finalDataLength
        finalData[2] = 0x05
        finalData[3] = headerBytesSubset[0]
        finalData[4] = headerBytesSubset[1]
        finalData[5] = headerBytesSubset[2]
        finalData[6] = headerBytesSubset[3]
        finalData[7] = 0x00
        finalData[8] = 0x01
        finalData[9] = 0x07
        
        // Copy encrypted block
        for i in 0..<encryptedBlock.count {
            finalData[10 + i] = encryptedBlock[i]
        }
        
        finalData[finalData.count - 2] = 0x00
        finalData[finalData.count - 1] = 0x5A  // 90 in unsigned byte
        
        // Calculate checksum
        var checksum = 0
        for i in 1..<(finalData.count - 2) {
            checksum += Int(finalData[i])
        }
        
        finalData[finalData.count - 2] = UInt8(checksum & 0xFF)
        
        print("Final encrypted data: \(bytesToHex(finalData))")
        
        return finalData
    }
}
