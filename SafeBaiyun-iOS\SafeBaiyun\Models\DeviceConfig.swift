import Foundation

/// Device configuration model for storing MAC address and encryption key
struct DeviceConfig: Codable, Equatable {
    let macAddress: String
    let encryptionKey: String
    
    init(macAddress: String, encryptionKey: String) {
        self.macAddress = macAddress.uppercased()
        self.encryptionKey = encryptionKey
    }
    
    /// Validates if the MAC address format is correct
    var isValidMacAddress: Bool {
        let macRegex = "^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$"
        let macPredicate = NSPredicate(format: "SELF MATCHES %@", macRegex)
        return macPredicate.evaluate(with: macAddress)
    }
    
    /// Validates if the encryption key is not empty
    var isValidEncryptionKey: Bool {
        return !encryptionKey.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }
    
    /// Validates if both MAC address and encryption key are valid
    var isValid: Bool {
        return isValidMacAddress && isValidEncryptionKey
    }
    
    /// Returns MAC address as byte array for Bluetooth operations
    var macAddressBytes: [UInt8] {
        let cleanMac = macAddress.replacingOccurrences(of: ":", with: "").replacingOccurrences(of: "-", with: "")
        var bytes: [UInt8] = []
        
        for i in stride(from: 0, to: cleanMac.count, by: 2) {
            let startIndex = cleanMac.index(cleanMac.startIndex, offsetBy: i)
            let endIndex = cleanMac.index(startIndex, offsetBy: 2)
            let byteString = String(cleanMac[startIndex..<endIndex])
            
            if let byte = UInt8(byteString, radix: 16) {
                bytes.append(byte)
            }
        }
        
        return bytes
    }
    
    /// Empty configuration for initial state
    static let empty = DeviceConfig(macAddress: "", encryptionKey: "")
}

/// Bluetooth connection state
enum BluetoothConnectionState {
    case disconnected
    case connecting
    case connected
    case unlocking
    case error(String)
    
    var description: String {
        switch self {
        case .disconnected:
            return "未连接"
        case .connecting:
            return "连接中..."
        case .connected:
            return "已连接"
        case .unlocking:
            return "开门中..."
        case .error(let message):
            return "错误: \(message)"
        }
    }
}
