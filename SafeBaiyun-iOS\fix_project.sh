#!/bin/bash

# SafeBaiyun iOS项目修复脚本
# 用于解决从Windows移植到Mac时的兼容性问题

echo "🔧 SafeBaiyun iOS项目修复脚本"
echo "================================"

PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PBXPROJ_FILE="$PROJECT_DIR/SafeBaiyun.xcodeproj/project.pbxproj"

echo "📁 项目目录: $PROJECT_DIR"
echo "📄 项目文件: $PBXPROJ_FILE"

# 检查项目文件是否存在
if [ ! -f "$PBXPROJ_FILE" ]; then
    echo "❌ 错误: 找不到项目文件 $PBXPROJ_FILE"
    exit 1
fi

echo ""
echo "🔍 检查项目文件状态..."

# 检查文件编码
file_encoding=$(file -b --mime-encoding "$PBXPROJ_FILE")
echo "📝 文件编码: $file_encoding"

# 检查行结束符
line_endings=$(file -b "$PBXPROJ_FILE" | grep -o "CRLF\|LF" | head -1)
echo "📄 行结束符: ${line_endings:-LF}"

# 修复行结束符（如果需要）
if [[ "$line_endings" == *"CRLF"* ]]; then
    echo "🔄 修复行结束符 (CRLF -> LF)..."
    # 创建备份
    cp "$PBXPROJ_FILE" "$PBXPROJ_FILE.crlf_backup"
    # 转换行结束符
    sed -i '' 's/\r$//' "$PBXPROJ_FILE"
    echo "✅ 行结束符已修复"
else
    echo "✅ 行结束符正常"
fi

# 验证项目文件结构
echo ""
echo "🔍 验证项目文件结构..."

# 检查是否包含有效的Xcode项目ID
invalid_ids=$(grep -c "1A2B3C4D5E6F7890AB" "$PBXPROJ_FILE" 2>/dev/null || echo "0")
valid_ids=$(grep -c "A1B2C3D4E5F67890123456" "$PBXPROJ_FILE" 2>/dev/null || echo "0")

echo "📊 无效ID数量: $invalid_ids"
echo "📊 有效ID数量: $valid_ids"

if [ "$invalid_ids" -gt 0 ]; then
    echo "⚠️  发现无效的占位符ID，项目文件可能需要进一步修复"
else
    echo "✅ 项目ID格式正常"
fi

# 检查关键section是否存在
sections=("PBXBuildFile" "PBXFileReference" "PBXGroup" "PBXNativeTarget" "PBXProject")
echo ""
echo "🔍 检查项目section完整性..."

for section in "${sections[@]}"; do
    if grep -q "Begin $section section" "$PBXPROJ_FILE"; then
        echo "✅ $section section 存在"
    else
        echo "❌ $section section 缺失"
    fi
done

# 检查文件权限
echo ""
echo "🔍 检查文件权限..."
ls -la "$PBXPROJ_FILE"

# 提供修复建议
echo ""
echo "💡 修复建议:"
echo "1. 项目文件已更新为使用有效的Xcode ID格式"
echo "2. 如果仍有问题，请尝试在Xcode中打开项目"
echo "3. 如果Xcode提示项目需要升级，请选择升级"
echo "4. 备份文件位置: $PBXPROJ_FILE.backup"

# 检查是否有备份文件
if [ -f "$PBXPROJ_FILE.backup" ]; then
    echo "5. 原始备份文件: $PBXPROJ_FILE.backup"
fi

echo ""
echo "🎉 修复脚本执行完成！"
echo "现在可以尝试在Xcode中打开项目了。"
