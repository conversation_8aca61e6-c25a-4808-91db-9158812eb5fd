import WidgetKit
import SwiftUI

// MARK: - Widget Entry
struct SafeBaiyunEntry: TimelineEntry {
    let date: Date
    let hasConfiguration: Bool
    let macAddress: String
}

// MARK: - Widget Provider
struct SafeBaiyunProvider: TimelineProvider {
    func placeholder(in context: Context) -> SafeBaiyunEntry {
        SafeBaiyunEntry(date: Date(), hasConfiguration: true, macAddress: "AA:BB:CC:DD:EE:FF")
    }
    
    func getSnapshot(in context: Context, completion: @escaping (SafeBaiyunEntry) -> ()) {
        let entry = SafeBaiyunEntry(date: Date(), hasConfiguration: true, macAddress: "AA:BB:CC:DD:EE:FF")
        completion(entry)
    }
    
    func getTimeline(in context: Context, completion: @escaping (Timeline<SafeBaiyunEntry>) -> ()) {
        // Check if configuration exists
        let userDefaults = UserDefaults.standard
        let macAddress = userDefaults.string(forKey: "mac_address") ?? ""
        let encryptionKey = userDefaults.string(forKey: "encryption_key") ?? ""
        let hasConfiguration = !macAddress.isEmpty && !encryptionKey.isEmpty
        
        let entry = SafeBaiyunEntry(
            date: Date(),
            hasConfiguration: hasConfiguration,
            macAddress: macAddress
        )
        
        // Update every hour
        let nextUpdate = Calendar.current.date(byAdding: .hour, value: 1, to: Date()) ?? Date()
        let timeline = Timeline(entries: [entry], policy: .after(nextUpdate))
        
        completion(timeline)
    }
}

// MARK: - Widget Views
struct SafeBaiyunWidgetEntryView: View {
    var entry: SafeBaiyunProvider.Entry
    @Environment(\.widgetFamily) var family
    
    var body: some View {
        switch family {
        case .systemMedium:
            MediumWidgetView(entry: entry)
        case .systemLarge:
            LargeWidgetView(entry: entry)
        default:
            SmallWidgetView(entry: entry)
        }
    }
}

// MARK: - Small Widget
struct SmallWidgetView: View {
    let entry: SafeBaiyunEntry
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: entry.hasConfiguration ? "lock.open" : "gear")
                .font(.system(size: 24))
                .foregroundColor(.white)
            
            Text(entry.hasConfiguration ? "开门" : "配置")
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.white)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(
            LinearGradient(
                gradient: Gradient(colors: [Color.blue, Color.blue.opacity(0.8)]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
        .widgetURL(URL(string: entry.hasConfiguration ? "safebaiyun://unlock" : "safebaiyun://config"))
    }
}

// MARK: - Medium Widget
struct MediumWidgetView: View {
    let entry: SafeBaiyunEntry
    
    var body: some View {
        HStack(spacing: 16) {
            VStack(alignment: .leading, spacing: 4) {
                Text("白云通")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                
                if entry.hasConfiguration {
                    Text("点击解锁")
                        .font(.subheadline)
                        .foregroundColor(.white.opacity(0.8))
                    
                    Text("MAC: \(String(entry.macAddress.prefix(8)))...")
                        .font(.caption2)
                        .foregroundColor(.white.opacity(0.6))
                        .lineLimit(1)
                } else {
                    Text("需要配置")
                        .font(.subheadline)
                        .foregroundColor(.white.opacity(0.8))
                }
            }
            
            Spacer()
            
            Button(intent: UnlockIntent()) {
                Image(systemName: entry.hasConfiguration ? "lock.open.fill" : "gear.circle.fill")
                    .font(.system(size: 32))
                    .foregroundColor(.white)
                    .background(
                        Circle()
                            .fill(Color.white.opacity(0.2))
                            .frame(width: 50, height: 50)
                    )
            }
            .buttonStyle(PlainButtonStyle())
        }
        .padding()
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(
            LinearGradient(
                gradient: Gradient(colors: [Color.blue, Color.blue.opacity(0.7)]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
    }
}

// MARK: - Large Widget
struct LargeWidgetView: View {
    let entry: SafeBaiyunEntry
    
    var body: some View {
        VStack(spacing: 20) {
            // Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("白云通")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                    
                    Text("平安白云门禁")
                        .font(.subheadline)
                        .foregroundColor(.white.opacity(0.8))
                }
                
                Spacer()
                
                Image(systemName: "building.2")
                    .font(.system(size: 40))
                    .foregroundColor(.white.opacity(0.6))
            }
            
            Spacer()
            
            // Main content
            VStack(spacing: 12) {
                if entry.hasConfiguration {
                    Button(intent: UnlockIntent()) {
                        HStack {
                            Image(systemName: "lock.open")
                                .font(.title2)
                            Text("门禁解锁")
                                .font(.headline)
                                .fontWeight(.semibold)
                        }
                        .foregroundColor(.blue)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.white)
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                    
                    Text("MAC: \(entry.macAddress)")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.7))
                        .lineLimit(1)
                } else {
                    VStack(spacing: 8) {
                        Image(systemName: "exclamationmark.triangle")
                            .font(.title2)
                            .foregroundColor(.white)
                        
                        Text("需要配置MAC地址和密钥")
                            .font(.subheadline)
                            .foregroundColor(.white)
                            .multilineTextAlignment(.center)
                        
                        Button(intent: ConfigIntent()) {
                            Text("立即配置")
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(.blue)
                                .padding(.horizontal, 16)
                                .padding(.vertical, 8)
                                .background(
                                    RoundedRectangle(cornerRadius: 8)
                                        .fill(Color.white)
                                )
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                }
            }
            
            Spacer()
        }
        .padding()
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(
            LinearGradient(
                gradient: Gradient(colors: [Color.blue, Color.blue.opacity(0.7)]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
    }
}

// MARK: - Widget Configuration
struct SafeBaiyunWidget: Widget {
    let kind: String = "SafeBaiyunWidget"
    
    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: SafeBaiyunProvider()) { entry in
            SafeBaiyunWidgetEntryView(entry: entry)
                .containerBackground(.fill.tertiary, for: .widget)
        }
        .configurationDisplayName("白云通")
        .description("快速访问门禁解锁功能")
        .supportedFamilies([.systemSmall, .systemMedium, .systemLarge])
    }
}

// MARK: - App Intents (iOS 16+)
@available(iOS 16.0, *)
struct UnlockIntent: AppIntent {
    static var title: LocalizedStringResource = "解锁门禁"
    static var description = IntentDescription("使用蓝牙解锁门禁设备")
    
    func perform() async throws -> some IntentResult {
        // Open app with unlock action
        return .result(opensIntent: OpenAppIntent())
    }
}

@available(iOS 16.0, *)
struct ConfigIntent: AppIntent {
    static var title: LocalizedStringResource = "配置设备"
    static var description = IntentDescription("配置门禁设备信息")
    
    func perform() async throws -> some IntentResult {
        // Open app with config action
        return .result(opensIntent: OpenAppIntent())
    }
}

@available(iOS 16.0, *)
struct OpenAppIntent: AppIntent {
    static var title: LocalizedStringResource = "打开白云通"
    
    func perform() async throws -> some IntentResult {
        return .result()
    }
}

// MARK: - Widget Bundle
@main
struct SafeBaiyunWidgetBundle: WidgetBundle {
    var body: some Widget {
        SafeBaiyunWidget()
    }
}

// MARK: - Previews
#Preview(as: .systemMedium) {
    SafeBaiyunWidget()
} timeline: {
    SafeBaiyunEntry(date: .now, hasConfiguration: true, macAddress: "AA:BB:CC:DD:EE:FF")
    SafeBaiyunEntry(date: .now, hasConfiguration: false, macAddress: "")
}
