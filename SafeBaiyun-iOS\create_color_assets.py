#!/usr/bin/env python3
"""
创建SafeBaiyun应用的颜色资源文件
为每个颜色集生成Contents.json配置文件
"""

import os
import json

def create_color_contents(color_name, light_color, dark_color=None):
    """创建颜色集的Contents.json内容"""
    if dark_color is None:
        dark_color = light_color
    
    contents = {
        "colors": [
            {
                "color": {
                    "color-space": "srgb",
                    "components": {
                        "alpha": "1.000",
                        "blue": f"{light_color[2]:.3f}",
                        "green": f"{light_color[1]:.3f}",
                        "red": f"{light_color[0]:.3f}"
                    }
                },
                "idiom": "universal"
            },
            {
                "appearances": [
                    {
                        "appearance": "luminosity",
                        "value": "dark"
                    }
                ],
                "color": {
                    "color-space": "srgb",
                    "components": {
                        "alpha": "1.000",
                        "blue": f"{dark_color[2]:.3f}",
                        "green": f"{dark_color[1]:.3f}",
                        "red": f"{dark_color[0]:.3f}"
                    }
                },
                "idiom": "universal"
            }
        ],
        "info": {
            "author": "xcode",
            "version": 1
        }
    }
    return contents

def main():
    """主函数"""
    print("🎨 创建SafeBaiyun应用颜色资源...")
    
    # 颜色定义 (RGB值，范围0-1)
    colors = {
        "AppBackground": {
            "light": (0.98, 0.98, 0.98),  # 浅灰色背景
            "dark": (0.1, 0.1, 0.1)       # 深色背景
        },
        "AppPrimary": {
            "light": (0.2, 0.4, 0.8),     # 蓝色主色
            "dark": (0.4, 0.6, 0.9)       # 浅蓝色（深色模式）
        },
        "AppSecondary": {
            "light": (0.5, 0.5, 0.5),     # 灰色次要色
            "dark": (0.7, 0.7, 0.7)       # 浅灰色（深色模式）
        },
        "AppSurface": {
            "light": (1.0, 1.0, 1.0),     # 白色表面
            "dark": (0.15, 0.15, 0.15)    # 深灰色表面
        },
        "ErrorColor": {
            "light": (0.9, 0.2, 0.2),     # 红色错误
            "dark": (1.0, 0.4, 0.4)       # 浅红色（深色模式）
        },
        "SuccessColor": {
            "light": (0.2, 0.7, 0.2),     # 绿色成功
            "dark": (0.4, 0.9, 0.4)       # 浅绿色（深色模式）
        },
        "WarningColor": {
            "light": (0.9, 0.6, 0.1),     # 橙色警告
            "dark": (1.0, 0.8, 0.3)       # 浅橙色（深色模式）
        },
        "WidgetGradientStart": {
            "light": (0.2, 0.4, 0.8),     # 渐变起始色
            "dark": (0.3, 0.5, 0.9)       # 深色模式渐变起始色
        },
        "WidgetGradientEnd": {
            "light": (0.4, 0.6, 0.9),     # 渐变结束色
            "dark": (0.5, 0.7, 1.0)       # 深色模式渐变结束色
        }
    }
    
    colors_dir = "SafeBaiyun/Assets.xcassets/Colors"
    
    # 确保Colors目录存在
    os.makedirs(colors_dir, exist_ok=True)
    
    created_count = 0
    
    for color_name, color_values in colors.items():
        colorset_dir = os.path.join(colors_dir, f"{color_name}.colorset")
        contents_file = os.path.join(colorset_dir, "Contents.json")
        
        # 确保colorset目录存在
        os.makedirs(colorset_dir, exist_ok=True)
        
        # 创建Contents.json
        contents = create_color_contents(
            color_name,
            color_values["light"],
            color_values["dark"]
        )
        
        with open(contents_file, 'w', encoding='utf-8') as f:
            json.dump(contents, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 创建颜色集: {color_name}")
        created_count += 1
    
    print(f"\n🎉 成功创建 {created_count} 个颜色资源文件！")
    print("📝 颜色说明:")
    print("  - AppPrimary: 应用主色调（蓝色）")
    print("  - AppSecondary: 次要色调（灰色）")
    print("  - AppBackground: 背景色")
    print("  - AppSurface: 表面色（卡片、按钮等）")
    print("  - ErrorColor: 错误状态色（红色）")
    print("  - SuccessColor: 成功状态色（绿色）")
    print("  - WarningColor: 警告状态色（橙色）")
    print("  - WidgetGradient*: Widget渐变色")

if __name__ == "__main__":
    main()
