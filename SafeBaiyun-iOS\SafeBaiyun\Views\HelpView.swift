import SwiftUI

struct HelpView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // Header
                    VStack(spacing: 8) {
                        Image(systemName: "questionmark.circle.fill")
                            .font(.system(size: 50))
                            .foregroundColor(.blue)
                        
                        Text("使用帮助")
                            .font(.title2)
                            .fontWeight(.semibold)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.top)
                    
                    // About section
                    HelpSection(title: "关于软件") {
                        Text("本软件是广州市白云区蓝牙门禁的离线版本，只需要门禁的MAC地址以及加密key即可开门，无需网络。")
                    }
                    
                    // Usage instructions
                    HelpSection(title: "使用方法") {
                        VStack(alignment: .leading, spacing: 12) {
                            HelpStep(number: "1", title: "提取MAC地址及加密密钥")
                            
                            VStack(alignment: .leading, spacing: 8) {
                                HelpSubStep(title: "1.1 Root方式提取")
                                Text("有 root 的Android 手机可以直接前往 /data/data/com.huacheng.baiyunuser/databases/目录 找到数据库文件 (32位 hash).db")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                
                                HelpSubStep(title: "1.2 无Root方式提取")
                                Text("使用MIUI的备份功能提取数据文件，前往 设置->我的设备->备份与恢复->手机备份 只选中平安回家这个软件进行备份即可，备份完成之后用 MT 管理器打开 /sdcard/MIUI/backup/AllBackup/时间/平安回家(com.huacheng.baiyunuser.bak)压缩包 然后在压缩包中找到apps/com.huacheng.baiyunuser/db/(32位 hash).db将其解压出来。")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                
                                HelpSubStep(title: "1.3 查看DB文件")
                                Text("随便找个支持查看 sqlite 数据库的软件，打开.db文件，查询t_device表， 其中 MAC_NUM 是 mac 地址 PRODUCT_KEY 就是加密key")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                            
                            HelpStep(number: "2", title: "点击软件右上角编辑按钮，将Mac地址及Key填进去，保存")
                            
                            HelpStep(number: "3", title: "开门")
                        }
                    }
                    
                    // iOS specific features
                    HelpSection(title: "iOS特色功能") {
                        VStack(alignment: .leading, spacing: 12) {
                            FeatureItem(
                                icon: "widget.small",
                                title: "桌面小组件",
                                description: "支持添加桌面小组件，快速访问开门功能"
                            )
                            
                            FeatureItem(
                                icon: "mic",
                                title: "Siri快捷指令",
                                description: "可以通过Siri语音指令快速开门"
                            )
                            
                            FeatureItem(
                                icon: "hand.tap",
                                title: "触觉反馈",
                                description: "操作时提供触觉反馈，提升使用体验"
                            )
                        }
                    }
                    
                    // Troubleshooting
                    HelpSection(title: "常见问题") {
                        VStack(alignment: .leading, spacing: 12) {
                            TroubleshootingItem(
                                question: "为什么连接不上门禁设备？",
                                answer: "请确保蓝牙已开启，MAC地址格式正确，并且在门禁设备附近。"
                            )
                            
                            TroubleshootingItem(
                                question: "如何确认MAC地址是否正确？",
                                answer: "MAC地址应该是6组两位十六进制数字，用冒号分隔，例如：AA:BB:CC:DD:EE:FF"
                            )
                            
                            TroubleshootingItem(
                                question: "开门失败怎么办？",
                                answer: "请检查加密密钥是否正确，确保设备距离门禁足够近，并重试操作。"
                            )
                        }
                    }
                    
                    // Footer
                    VStack(spacing: 8) {
                        Text("版本 1.1")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Text("iOS版本移植")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.top, 20)
                }
                .padding()
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                }
            }
        }
    }
}

// MARK: - Helper Views

struct HelpSection<Content: View>: View {
    let title: String
    let content: Content
    
    init(title: String, @ViewBuilder content: () -> Content) {
        self.title = title
        self.content = content()
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text(title)
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
            
            content
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
        )
    }
}

struct HelpStep: View {
    let number: String
    let title: String
    
    var body: some View {
        HStack(alignment: .top, spacing: 8) {
            Text(number)
                .font(.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(.white)
                .frame(width: 24, height: 24)
                .background(Circle().fill(Color.blue))
            
            Text(title)
                .font(.subheadline)
                .fontWeight(.medium)
        }
    }
}

struct HelpSubStep: View {
    let title: String
    
    var body: some View {
        Text(title)
            .font(.subheadline)
            .fontWeight(.medium)
            .foregroundColor(.blue)
            .padding(.leading, 16)
    }
}

struct FeatureItem: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(.blue)
                .frame(width: 30)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
    }
}

struct TroubleshootingItem: View {
    let question: String
    let answer: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 6) {
            Text("Q: \(question)")
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.primary)
            
            Text("A: \(answer)")
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
}

#Preview {
    HelpView()
}
