import Foundation
import CoreBluetooth

/// 蓝牙调试工具，用于诊断连接问题
class BluetoothDebugger: NSObject, ObservableObject {
    static let shared = BluetoothDebugger()
    
    @Published var debugLogs: [String] = []
    @Published var discoveredDevices: [DiscoveredDevice] = []
    
    private var centralManager: CBCentralManager!
    private var isScanning = false
    
    struct DiscoveredDevice {
        let peripheral: CBPeripheral
        let advertisementData: [String: Any]
        let rssi: NSNumber
        let timestamp: Date
        
        var displayName: String {
            return peripheral.name ?? "Unknown Device"
        }
        
        var identifier: String {
            return peripheral.identifier.uuidString
        }
        
        var serviceUUIDs: [String] {
            if let uuids = advertisementData[CBAdvertisementDataServiceUUIDsKey] as? [CBUUID] {
                return uuids.map { $0.uuidString }
            }
            return []
        }
    }
    
    override init() {
        super.init()
        centralManager = CBCentralManager(delegate: self, queue: nil)
    }
    
    func startDeepScan() {
        guard centralManager.state == .poweredOn else {
            addLog("❌ 蓝牙未开启或不可用")
            return
        }
        
        discoveredDevices.removeAll()
        addLog("🔍 开始深度扫描所有BLE设备...")
        
        isScanning = true
        // 扫描所有设备，不过滤服务UUID
        centralManager.scanForPeripherals(withServices: nil, options: [
            CBCentralManagerScanOptionAllowDuplicatesKey: true
        ])
        
        // 30秒后停止扫描
        DispatchQueue.main.asyncAfter(deadline: .now() + 30) {
            self.stopScan()
        }
    }
    
    func stopScan() {
        guard isScanning else { return }
        
        centralManager.stopScan()
        isScanning = false
        addLog("⏹️ 扫描已停止，共发现 \(discoveredDevices.count) 个设备")
    }
    
    func testConnectToDevice(_ device: DiscoveredDevice) {
        addLog("🔗 尝试连接设备: \(device.displayName)")
        centralManager.connect(device.peripheral, options: nil)
    }
    
    func addLog(_ message: String) {
        let timestamp = DateFormatter.localizedString(from: Date(), dateStyle: .none, timeStyle: .medium)
        let logMessage = "[\(timestamp)] \(message)"
        
        DispatchQueue.main.async {
            self.debugLogs.append(logMessage)
            print(logMessage)
        }
    }
    
    func clearLogs() {
        debugLogs.removeAll()
    }
    
    func exportLogs() -> String {
        return debugLogs.joined(separator: "\n")
    }
    
    /// 验证设备配置
    func validateConfiguration(_ config: DeviceConfig) -> [String] {
        var issues: [String] = []
        
        // 检查MAC地址格式
        let macPattern = "^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$"
        let macRegex = try? NSRegularExpression(pattern: macPattern)
        let macRange = NSRange(location: 0, length: config.macAddress.count)
        
        if macRegex?.firstMatch(in: config.macAddress, options: [], range: macRange) == nil {
            issues.append("MAC地址格式不正确: \(config.macAddress)")
        }
        
        // 检查加密密钥格式
        if config.encryptionKey.count % 2 != 0 {
            issues.append("加密密钥长度必须为偶数: \(config.encryptionKey.count)")
        }
        
        let keyPattern = "^[0-9A-Fa-f]+$"
        let keyRegex = try? NSRegularExpression(pattern: keyPattern)
        let keyRange = NSRange(location: 0, length: config.encryptionKey.count)
        
        if keyRegex?.firstMatch(in: config.encryptionKey, options: [], range: keyRange) == nil {
            issues.append("加密密钥包含非十六进制字符: \(config.encryptionKey)")
        }
        
        return issues
    }
    
    /// 分析广告数据
    func analyzeAdvertisementData(_ data: [String: Any]) -> String {
        var analysis = "广告数据分析:\n"
        
        for (key, value) in data {
            switch key {
            case CBAdvertisementDataLocalNameKey:
                analysis += "• 设备名称: \(value)\n"
            case CBAdvertisementDataServiceUUIDsKey:
                if let uuids = value as? [CBUUID] {
                    analysis += "• 服务UUID: \(uuids.map { $0.uuidString }.joined(separator: ", "))\n"
                }
            case CBAdvertisementDataManufacturerDataKey:
                if let data = value as? Data {
                    analysis += "• 制造商数据: \(data.map { String(format: "%02X", $0) }.joined(separator: " "))\n"
                }
            case CBAdvertisementDataTxPowerLevelKey:
                analysis += "• 发射功率: \(value) dBm\n"
            case CBAdvertisementDataIsConnectable:
                analysis += "• 可连接: \(value)\n"
            default:
                analysis += "• \(key): \(value)\n"
            }
        }
        
        return analysis
    }
}

// MARK: - CBCentralManagerDelegate
extension BluetoothDebugger: CBCentralManagerDelegate {
    func centralManagerDidUpdateState(_ central: CBCentralManager) {
        switch central.state {
        case .poweredOn:
            addLog("✅ 蓝牙已开启")
        case .poweredOff:
            addLog("❌ 蓝牙已关闭")
        case .unauthorized:
            addLog("❌ 蓝牙权限被拒绝")
        case .unsupported:
            addLog("❌ 设备不支持蓝牙")
        case .resetting:
            addLog("⚠️ 蓝牙正在重置")
        case .unknown:
            addLog("⚠️ 蓝牙状态未知")
        @unknown default:
            addLog("⚠️ 未知蓝牙状态")
        }
    }
    
    func centralManager(_ central: CBCentralManager, didDiscover peripheral: CBPeripheral, advertisementData: [String : Any], rssi RSSI: NSNumber) {
        let device = DiscoveredDevice(
            peripheral: peripheral,
            advertisementData: advertisementData,
            rssi: RSSI,
            timestamp: Date()
        )
        
        // 避免重复添加相同设备
        if !discoveredDevices.contains(where: { $0.identifier == device.identifier }) {
            discoveredDevices.append(device)
            
            let serviceInfo = device.serviceUUIDs.isEmpty ? "无服务UUID" : "服务: \(device.serviceUUIDs.joined(separator: ", "))"
            addLog("📱 发现设备: \(device.displayName) (\(device.identifier)) RSSI: \(RSSI) dBm - \(serviceInfo)")
        }
    }
    
    func centralManager(_ central: CBCentralManager, didConnect peripheral: CBPeripheral) {
        addLog("✅ 已连接到设备: \(peripheral.name ?? "Unknown")")
        peripheral.delegate = self
        peripheral.discoverServices(nil)
    }
    
    func centralManager(_ central: CBCentralManager, didFailToConnect peripheral: CBPeripheral, error: Error?) {
        addLog("❌ 连接失败: \(peripheral.name ?? "Unknown") - \(error?.localizedDescription ?? "未知错误")")
    }
    
    func centralManager(_ central: CBCentralManager, didDisconnectPeripheral peripheral: CBPeripheral, error: Error?) {
        if let error = error {
            addLog("⚠️ 设备断开连接: \(peripheral.name ?? "Unknown") - \(error.localizedDescription)")
        } else {
            addLog("🔌 设备正常断开: \(peripheral.name ?? "Unknown")")
        }
    }
}

// MARK: - CBPeripheralDelegate
extension BluetoothDebugger: CBPeripheralDelegate {
    func peripheral(_ peripheral: CBPeripheral, didDiscoverServices error: Error?) {
        if let error = error {
            addLog("❌ 服务发现失败: \(error.localizedDescription)")
            return
        }
        
        guard let services = peripheral.services else {
            addLog("❌ 未找到任何服务")
            return
        }
        
        addLog("🔍 发现 \(services.count) 个服务:")
        for service in services {
            addLog("  • 服务UUID: \(service.uuid)")
            peripheral.discoverCharacteristics(nil, for: service)
        }
    }
    
    func peripheral(_ peripheral: CBPeripheral, didDiscoverCharacteristicsFor service: CBService, error: Error?) {
        if let error = error {
            addLog("❌ 特征发现失败: \(error.localizedDescription)")
            return
        }
        
        guard let characteristics = service.characteristics else {
            addLog("❌ 服务 \(service.uuid) 无特征")
            return
        }
        
        addLog("🔍 服务 \(service.uuid) 发现 \(characteristics.count) 个特征:")
        for characteristic in characteristics {
            let properties = characteristic.properties
            var propStrings: [String] = []
            
            if properties.contains(.read) { propStrings.append("读") }
            if properties.contains(.write) { propStrings.append("写") }
            if properties.contains(.writeWithoutResponse) { propStrings.append("无响应写") }
            if properties.contains(.notify) { propStrings.append("通知") }
            if properties.contains(.indicate) { propStrings.append("指示") }
            
            addLog("  • 特征UUID: \(characteristic.uuid) - 属性: \(propStrings.joined(separator: ", "))")
        }
    }
}
