import SwiftUI

/// 蓝牙调试界面，用于诊断连接问题
struct BluetoothDebugView: View {
    @StateObject private var debugger = BluetoothDebugger.shared
    @State private var showingDeviceDetails = false
    @State private var selectedDevice: BluetoothDebugger.DiscoveredDevice?
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 控制按钮
                HStack(spacing: 16) {
                    But<PERSON>(action: {
                        debugger.startDeepScan()
                    }) {
                        HStack {
                            Image(systemName: "magnifyingglass")
                            Text("开始扫描")
                        }
                        .foregroundColor(.white)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(Color.blue)
                        .cornerRadius(8)
                    }
                    
                    Button(action: {
                        debugger.stopScan()
                    }) {
                        HStack {
                            Image(systemName: "stop.fill")
                            Text("停止扫描")
                        }
                        .foregroundColor(.white)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(Color.red)
                        .cornerRadius(8)
                    }
                    
                    But<PERSON>(action: {
                        debugger.clearLogs()
                    }) {
                        HStack {
                            Image(systemName: "trash")
                            Text("清除日志")
                        }
                        .foregroundColor(.white)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(Color.gray)
                        .cornerRadius(8)
                    }
                }
                .padding()
                
                Divider()
                
                // 设备列表和日志
                TabView {
                    // 发现的设备
                    VStack {
                        Text("发现的设备 (\(debugger.discoveredDevices.count))")
                            .font(.headline)
                            .padding()
                        
                        List(debugger.discoveredDevices, id: \.identifier) { device in
                            DeviceRowView(device: device) {
                                selectedDevice = device
                                showingDeviceDetails = true
                            } onConnect: {
                                debugger.testConnectToDevice(device)
                            }
                        }
                    }
                    .tabItem {
                        Image(systemName: "antenna.radiowaves.left.and.right")
                        Text("设备")
                    }
                    
                    // 调试日志
                    VStack {
                        Text("调试日志 (\(debugger.debugLogs.count))")
                            .font(.headline)
                            .padding()
                        
                        ScrollViewReader { proxy in
                            ScrollView {
                                LazyVStack(alignment: .leading, spacing: 4) {
                                    ForEach(Array(debugger.debugLogs.enumerated()), id: \.offset) { index, log in
                                        Text(log)
                                            .font(.system(.caption, design: .monospaced))
                                            .padding(.horizontal, 8)
                                            .padding(.vertical, 2)
                                            .id(index)
                                    }
                                }
                            }
                            .onChange(of: debugger.debugLogs.count) { _ in
                                if !debugger.debugLogs.isEmpty {
                                    proxy.scrollTo(debugger.debugLogs.count - 1, anchor: .bottom)
                                }
                            }
                        }
                    }
                    .tabItem {
                        Image(systemName: "doc.text")
                        Text("日志")
                    }
                }
            }
            .navigationTitle("蓝牙调试")
            .navigationBarTitleDisplayMode(.inline)
            .sheet(isPresented: $showingDeviceDetails) {
                if let device = selectedDevice {
                    DeviceDetailView(device: device)
                }
            }
        }
    }
}

struct DeviceRowView: View {
    let device: BluetoothDebugger.DiscoveredDevice
    let onTap: () -> Void
    let onConnect: () -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack {
                VStack(alignment: .leading) {
                    Text(device.displayName)
                        .font(.headline)
                    Text(device.identifier)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                VStack(alignment: .trailing) {
                    Text("\(device.rssi) dBm")
                        .font(.caption)
                        .foregroundColor(rssiColor(device.rssi.intValue))
                    
                    Button("连接") {
                        onConnect()
                    }
                    .font(.caption)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(4)
                }
            }
            
            if !device.serviceUUIDs.isEmpty {
                Text("服务: \(device.serviceUUIDs.joined(separator: ", "))")
                    .font(.caption)
                    .foregroundColor(.blue)
            }
        }
        .padding(.vertical, 4)
        .contentShape(Rectangle())
        .onTapGesture {
            onTap()
        }
    }
    
    private func rssiColor(_ rssi: Int) -> Color {
        switch rssi {
        case -50...0:
            return .green
        case -70..<(-50):
            return .orange
        default:
            return .red
        }
    }
}

struct DeviceDetailView: View {
    let device: BluetoothDebugger.DiscoveredDevice
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 16) {
                    // 基本信息
                    GroupBox("基本信息") {
                        VStack(alignment: .leading, spacing: 8) {
                            InfoRow(title: "设备名称", value: device.displayName)
                            InfoRow(title: "标识符", value: device.identifier)
                            InfoRow(title: "信号强度", value: "\(device.rssi) dBm")
                            InfoRow(title: "发现时间", value: DateFormatter.localizedString(from: device.timestamp, dateStyle: .short, timeStyle: .medium))
                        }
                    }
                    
                    // 服务UUID
                    if !device.serviceUUIDs.isEmpty {
                        GroupBox("广播的服务") {
                            VStack(alignment: .leading, spacing: 4) {
                                ForEach(device.serviceUUIDs, id: \.self) { uuid in
                                    Text(uuid)
                                        .font(.system(.caption, design: .monospaced))
                                        .padding(.vertical, 2)
                                }
                            }
                        }
                    }
                    
                    // 广告数据分析
                    GroupBox("广告数据分析") {
                        Text(BluetoothDebugger.shared.analyzeAdvertisementData(device.advertisementData))
                            .font(.system(.caption, design: .monospaced))
                    }
                    
                    // 兼容性检查
                    GroupBox("兼容性检查") {
                        VStack(alignment: .leading, spacing: 8) {
                            let targetServiceUUID = "14839ac4-7d7e-415c-9a42-167340cf2339"
                            let hasTargetService = device.serviceUUIDs.contains { $0.lowercased() == targetServiceUUID.lowercased() }
                            
                            HStack {
                                Image(systemName: hasTargetService ? "checkmark.circle.fill" : "xmark.circle.fill")
                                    .foregroundColor(hasTargetService ? .green : .red)
                                Text("目标服务UUID匹配")
                            }
                            
                            let hasStrongSignal = device.rssi.intValue > -70
                            HStack {
                                Image(systemName: hasStrongSignal ? "checkmark.circle.fill" : "exclamationmark.triangle.fill")
                                    .foregroundColor(hasStrongSignal ? .green : .orange)
                                Text("信号强度充足")
                            }
                        }
                    }
                }
                .padding()
            }
            .navigationTitle("设备详情")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(trailing: Button("关闭") {
                presentationMode.wrappedValue.dismiss()
            })
        }
    }
}

struct InfoRow: View {
    let title: String
    let value: String
    
    var body: some View {
        HStack {
            Text(title + ":")
                .fontWeight(.medium)
            Spacer()
            Text(value)
                .foregroundColor(.secondary)
        }
    }
}

struct BluetoothDebugView_Previews: PreviewProvider {
    static var previews: some View {
        BluetoothDebugView()
    }
}
