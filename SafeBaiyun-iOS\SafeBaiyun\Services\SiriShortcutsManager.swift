import Foundation
import Intents
import IntentsUI

/// Manager for Siri Shortcuts integration
@available(iOS 12.0, *)
class SiriShortcutsManager {
    static let shared = SiriShortcutsManager()
    
    private init() {}
    
    /// Create and donate unlock shortcut to Siri
    func donateUnlockShortcut() {
        let activity = NSUserActivity(activityType: "cn.huacheng.safebaiyun.unlock")
        activity.title = "门禁解锁"
        activity.userInfo = ["action": "unlock"]
        activity.isEligibleForSearch = true
        activity.isEligibleForPrediction = true
        activity.persistentIdentifier = "unlock_door"
        
        // Set suggested invocation phrase
        activity.suggestedInvocationPhrase = "开门"
        
        // Donate to Siri
        activity.becomeCurrent()
        
        print("已向Siri捐赠解锁快捷指令")
    }
    
    /// Create shortcut for adding to Siri
    func createUnlockShortcut() -> INShortcut? {
        let activity = NSUserActivity(activityType: "cn.huacheng.safebaiyun.unlock")
        activity.title = "门禁解锁"
        activity.userInfo = ["action": "unlock"]
        activity.isEligibleForSearch = true
        activity.isEligibleForPrediction = true
        activity.persistentIdentifier = "unlock_door"
        activity.suggestedInvocationPhrase = "开门"
        
        return INShortcut(userActivity: activity)
    }
    
    /// Check if shortcuts are available
    func areShortcutsAvailable() -> Bool {
        return INPreferences.siriAuthorizationStatus() != .denied
    }
    
    /// Request Siri authorization
    func requestSiriAuthorization(completion: @escaping (Bool) -> Void) {
        INPreferences.requestSiriAuthorization { status in
            DispatchQueue.main.async {
                completion(status == .authorized)
            }
        }
    }
}

/// Extension to handle Siri shortcuts in the app
extension SafeBaiyunApp {
    func handleSiriShortcut(_ userActivity: NSUserActivity) {
        guard userActivity.activityType == "cn.huacheng.safebaiyun.unlock",
              let userInfo = userActivity.userInfo,
              let action = userInfo["action"] as? String,
              action == "unlock" else {
            return
        }
        
        // Trigger unlock action
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            BluetoothManager.shared.unlock()
        }
    }
}
