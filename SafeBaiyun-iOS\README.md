# 白云通 iOS版

广州市白云区蓝牙门禁的离线版本iOS应用，只需要门禁的MAC地址以及加密key即可开门，无需网络。

## 功能特性

### 核心功能
- 🔓 **蓝牙门禁解锁** - 使用蓝牙LE连接门禁设备进行解锁
- ⚙️ **设备配置** - 简单易用的MAC地址和密钥配置界面
- 📱 **原生iOS体验** - 遵循iOS人机界面指南的原生设计

### iOS特色功能
- 🏠 **桌面小组件** - 支持小、中、大三种尺寸的桌面小组件
- 🗣️ **Siri快捷指令** - 支持语音"开门"指令
- 📳 **触觉反馈** - 丰富的触觉反馈提升用户体验
- 🔗 **深度链接** - 支持URL Scheme快速启动功能

## 系统要求

- iOS 15.0 或更高版本
- 支持蓝牙LE的设备
- iPhone、iPad 兼容

## 安装说明

1. 使用Xcode打开 `SafeBaiyun.xcodeproj`
2. 选择目标设备或模拟器
3. 点击运行按钮进行编译和安装

## 使用方法

### 1. 提取MAC地址及加密密钥

#### Root方式
有 root 的Android 手机可以直接前往 `/data/data/com.huacheng.baiyunuser/databases/` 目录找到数据库文件 `(32位 hash).db`

#### 无Root方式
使用手机的备份功能提取数据文件，以 MIUI 的备份举例：
1. 前往 设置->我的设备->备份与恢复->手机备份
2. 只选中`平安回家`这个软件进行备份
3. 用文件管理器打开 `/sdcard/MIUI/backup/AllBackup/时间/平安回家(com.huacheng.baiyunuser.bak)` 压缩包
4. 在压缩包中找到 `apps/com.huacheng.baiyunuser/db/(32位 hash).db` 将其解压出来

#### 查看DB文件
使用支持查看 sqlite 数据库的软件，打开.db文件，查询 `t_device` 表：
- `MAC_NUM` 是 MAC 地址
- `PRODUCT_KEY` 就是加密key

### 2. 配置应用
1. 点击应用右上角编辑按钮
2. 将MAC地址及Key填入对应字段
3. 点击保存

### 3. 使用门禁解锁
- 点击主界面的"门禁解锁"按钮
- 或使用桌面小组件快速解锁
- 或通过Siri语音指令"开门"

## 项目结构

```
SafeBaiyun-iOS/
├── SafeBaiyun/                 # 主应用
│   ├── App/                    # 应用入口
│   ├── Views/                  # SwiftUI视图
│   ├── Services/               # 业务逻辑服务
│   ├── Models/                 # 数据模型
│   ├── Utilities/              # 工具类
│   └── Resources/              # 资源文件
├── SafeBaiyunWidget/           # 桌面小组件
└── SafeBaiyun.xcodeproj        # Xcode项目文件
```

## 技术实现

### 核心技术栈
- **SwiftUI** - 现代化的用户界面框架
- **Core Bluetooth** - 蓝牙LE通信
- **WidgetKit** - 桌面小组件
- **Intents** - Siri快捷指令集成
- **CommonCrypto** - DES加密算法

### 加密算法
完全复制Android版本的DES加密算法，确保与门禁设备的完全兼容：
- DES/ECB/NoPadding 加密模式
- 自定义数据包格式
- 校验和计算

### 蓝牙通信
- 使用Core Bluetooth框架进行BLE通信
- 自动设备发现和连接
- 10秒连接超时机制
- 完整的错误处理和状态管理

## 开发说明

### 环境要求
- Xcode 15.0+
- Swift 5.9+
- iOS 15.0+ SDK

### 构建配置
- 开发团队ID需要在项目设置中配置
- 蓝牙权限已在Info.plist中配置
- 支持iPhone和iPad

### 调试说明
1. 蓝牙功能需要在真机上测试
2. 小组件需要添加到桌面进行测试
3. Siri快捷指令需要在设置中手动添加

## 版本历史

### v1.1 (当前版本)
- ✅ 完整的iOS原生界面
- ✅ 蓝牙LE门禁解锁功能
- ✅ 桌面小组件支持
- ✅ Siri快捷指令集成
- ✅ 触觉反馈支持
- ✅ 深度链接支持

## 许可证

本项目基于原Android版本进行iOS移植，保持相同的开源许可证。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 联系方式

如有问题或建议，请通过GitHub Issues联系。
