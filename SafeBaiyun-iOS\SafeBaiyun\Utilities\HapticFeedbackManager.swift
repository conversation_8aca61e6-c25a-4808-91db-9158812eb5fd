import UIKit

/// Manager for haptic feedback throughout the app
class HapticFeedbackManager {
    static let shared = HapticFeedbackManager()
    
    private init() {}
    
    /// Light impact feedback for button taps
    func lightImpact() {
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
    }
    
    /// Medium impact feedback for important actions
    func mediumImpact() {
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
    }
    
    /// Heavy impact feedback for critical actions
    func heavyImpact() {
        let impactFeedback = UIImpactFeedbackGenerator(style: .heavy)
        impactFeedback.impactOccurred()
    }
    
    /// Success notification feedback
    func success() {
        let notificationFeedback = UINotificationFeedbackGenerator()
        notificationFeedback.notificationOccurred(.success)
    }
    
    /// Warning notification feedback
    func warning() {
        let notificationFeedback = UINotificationFeedbackGenerator()
        notificationFeedback.notificationOccurred(.warning)
    }
    
    /// Error notification feedback
    func error() {
        let notificationFeedback = UINotificationFeedbackGenerator()
        notificationFeedback.notificationOccurred(.error)
    }
    
    /// Selection feedback for picker changes
    func selection() {
        let selectionFeedback = UISelectionFeedbackGenerator()
        selectionFeedback.selectionChanged()
    }
}

/// Extension for convenient haptic feedback in SwiftUI views
extension View {
    func hapticFeedback(_ style: HapticFeedbackStyle) -> some View {
        self.onTapGesture {
            switch style {
            case .light:
                HapticFeedbackManager.shared.lightImpact()
            case .medium:
                HapticFeedbackManager.shared.mediumImpact()
            case .heavy:
                HapticFeedbackManager.shared.heavyImpact()
            case .success:
                HapticFeedbackManager.shared.success()
            case .warning:
                HapticFeedbackManager.shared.warning()
            case .error:
                HapticFeedbackManager.shared.error()
            case .selection:
                HapticFeedbackManager.shared.selection()
            }
        }
    }
}

enum HapticFeedbackStyle {
    case light
    case medium
    case heavy
    case success
    case warning
    case error
    case selection
}
