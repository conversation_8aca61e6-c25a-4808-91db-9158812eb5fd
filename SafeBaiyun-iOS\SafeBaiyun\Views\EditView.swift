import SwiftUI

struct EditView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var dataRepository = DataRepository.shared
    
    @State private var macAddress: String = ""
    @State private var encryptionKey: String = ""
    @State private var showAlert = false
    @State private var alertMessage = ""
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // Header
                VStack(spacing: 8) {
                    Image(systemName: "gear")
                        .font(.system(size: 50))
                        .foregroundColor(.blue)
                    
                    Text("设备配置")
                        .font(.title2)
                        .fontWeight(.semibold)
                    
                    Text("请输入门禁设备的MAC地址和加密密钥")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .padding(.top)
                
                // Form
                VStack(spacing: 16) {
                    // MAC Address field
                    VStack(alignment: .leading, spacing: 8) {
                        Text("MAC地址")
                            .font(.headline)
                            .foregroundColor(.primary)
                        
                        TextField("例如: AA:BB:CC:DD:EE:FF", text: $macAddress)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            .autocapitalization(.allCharacters)
                            .disableAutocorrection(true)
                            .onChange(of: macAddress) { newValue in
                                // Format MAC address as user types
                                macAddress = formatMacAddress(newValue)
                            }
                        
                        if !macAddress.isEmpty && !isValidMacAddress(macAddress) {
                            Text("MAC地址格式不正确")
                                .font(.caption)
                                .foregroundColor(.red)
                        }
                    }
                    
                    // Encryption Key field
                    VStack(alignment: .leading, spacing: 8) {
                        Text("加密密钥")
                            .font(.headline)
                            .foregroundColor(.primary)
                        
                        TextField("请输入加密密钥", text: $encryptionKey)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            .disableAutocorrection(true)
                        
                        if !encryptionKey.isEmpty && encryptionKey.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                            Text("加密密钥不能为空")
                                .font(.caption)
                                .foregroundColor(.red)
                        }
                    }
                }
                .padding(.horizontal)
                
                Spacer()
                
                // Help text
                VStack(spacing: 8) {
                    Text("如何获取MAC地址和密钥？")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.blue)
                    
                    Text("请参考帮助页面中的详细说明，通过备份或Root方式提取原始应用的数据库文件。")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                }
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.blue.opacity(0.1))
                )
                .padding(.horizontal)
                
                // Buttons
                HStack(spacing: 16) {
                    Button("取消") {
                        dismiss()
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.gray.opacity(0.2))
                    )
                    
                    Button("保存") {
                        saveConfiguration()
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(canSave ? Color.blue : Color.gray.opacity(0.2))
                    )
                    .foregroundColor(canSave ? .white : .gray)
                    .disabled(!canSave)
                }
                .padding(.horizontal)
                .padding(.bottom)
            }
            .navigationBarHidden(true)
        }
        .onAppear {
            loadCurrentConfiguration()
        }
        .alert("提示", isPresented: $showAlert) {
            Button("确定", role: .cancel) { }
        } message: {
            Text(alertMessage)
        }
    }
    
    // MARK: - Computed Properties
    
    private var canSave: Bool {
        return isValidMacAddress(macAddress) && 
               !encryptionKey.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }
    
    // MARK: - Methods
    
    private func loadCurrentConfiguration() {
        let config = dataRepository.getConfiguration()
        macAddress = config.macAddress
        encryptionKey = config.encryptionKey
    }
    
    private func saveConfiguration() {
        let config = DeviceConfig(
            macAddress: macAddress.trimmingCharacters(in: .whitespacesAndNewlines),
            encryptionKey: encryptionKey.trimmingCharacters(in: .whitespacesAndNewlines)
        )
        
        dataRepository.saveConfiguration(config)
        
        // Haptic feedback
        let notificationFeedback = UINotificationFeedbackGenerator()
        notificationFeedback.notificationOccurred(.success)
        
        alertMessage = "配置保存成功！"
        showAlert = true
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            dismiss()
        }
    }
    
    private func isValidMacAddress(_ mac: String) -> Bool {
        let macRegex = "^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$"
        let macPredicate = NSPredicate(format: "SELF MATCHES %@", macRegex)
        return macPredicate.evaluate(with: mac)
    }
    
    private func formatMacAddress(_ input: String) -> String {
        // Remove all non-hex characters
        let cleanInput = input.uppercased().replacingOccurrences(of: "[^0-9A-F]", with: "", options: .regularExpression)
        
        // Limit to 12 characters (6 bytes)
        let limitedInput = String(cleanInput.prefix(12))
        
        // Add colons every 2 characters
        var formatted = ""
        for (index, character) in limitedInput.enumerated() {
            if index > 0 && index % 2 == 0 {
                formatted += ":"
            }
            formatted += String(character)
        }
        
        return formatted
    }
}

#Preview {
    EditView()
}
