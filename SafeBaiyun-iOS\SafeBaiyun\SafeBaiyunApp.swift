import SwiftUI

@main
struct SafeBaiyunApp: App {
    var body: some Scene {
        WindowGroup {
            ContentView()
                .onOpenURL { url in
                    handleDeepLink(url)
                }
        }
    }

    private func handleDeepLink(_ url: URL) {
        guard url.scheme == "safebaiyun" else { return }

        switch url.host {
        case "unlock":
            // Trigger unlock action
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                BluetoothManager.shared.unlock()
            }
        case "config":
            // Open configuration (handled by UI state)
            break
        default:
            break
        }
    }
}
