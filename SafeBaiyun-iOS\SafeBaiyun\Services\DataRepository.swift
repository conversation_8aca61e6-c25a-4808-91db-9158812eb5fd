import Foundation

/// Data repository for managing device configuration using UserDefaults
class DataRepository: ObservableObject {
    static let shared = DataRepository()
    
    private let userDefaults = UserDefaults.standard
    private let macAddressKey = "mac_address"
    private let encryptionKeyKey = "encryption_key"
    
    @Published var deviceConfig: DeviceConfig = DeviceConfig.empty
    
    private init() {
        loadConfiguration()
    }
    
    /// Load configuration from UserDefaults
    private func loadConfiguration() {
        let macAddress = userDefaults.string(forKey: macAddressKey) ?? ""
        let encryptionKey = userDefaults.string(forKey: encryptionKeyKey) ?? ""
        
        deviceConfig = DeviceConfig(macAddress: macAddress, encryptionKey: encryptionKey)
    }
    
    /// Save configuration to UserDefaults
    func saveConfiguration(_ config: DeviceConfig) {
        userDefaults.set(config.macAddress, forKey: macAddressKey)
        userDefaults.set(config.encryptionKey, forKey: encryptionKey<PERSON>ey)
        
        deviceConfig = config
        
        // Notify observers
        objectWillChange.send()
    }
    
    /// Get current configuration
    func getConfiguration() -> DeviceConfig {
        return deviceConfig
    }
    
    /// Check if configuration is valid and complete
    func hasValidConfiguration() -> Bool {
        return deviceConfig.isValid
    }
    
    /// Clear all stored configuration
    func clearConfiguration() {
        userDefaults.removeObject(forKey: macAddressKey)
        userDefaults.removeObject(forKey: encryptionKeyKey)
        
        deviceConfig = DeviceConfig.empty
        objectWillChange.send()
    }
    
    /// Update MAC address only
    func updateMacAddress(_ macAddress: String) {
        let newConfig = DeviceConfig(macAddress: macAddress, encryptionKey: deviceConfig.encryptionKey)
        saveConfiguration(newConfig)
    }
    
    /// Update encryption key only
    func updateEncryptionKey(_ encryptionKey: String) {
        let newConfig = DeviceConfig(macAddress: deviceConfig.macAddress, encryptionKey: encryptionKey)
        saveConfiguration(newConfig)
    }
}
